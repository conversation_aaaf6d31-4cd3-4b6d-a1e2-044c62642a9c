#!/usr/bin/env python3
"""
🔧 Test des dashboards corrigés BIMEX
"""

import requests
import time
from datetime import datetime

def test_dashboard_urls():
    """Teste les URLs des dashboards après correction"""
    
    print("🔧 Test des Dashboards Corrigés BIMEX")
    print("=" * 50)
    
    base_url = "http://localhost:8010"
    test_project = "basic2"
    
    # URLs à tester après orchestration
    dashboards = [
        ("ERP Dashboard", f"{base_url}/erp-dashboard?project_id={test_project}"),
        ("Superset Dashboard", f"{base_url}/superset-dashboard?project={test_project}"),
        ("IFC Viewer", f"{base_url}/ifc-viewer?project_id={test_project}"),
        ("n8n Setup", "http://localhost:5678/setup")
    ]
    
    print(f"🎯 Projet de test: {test_project}")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S')}")
    
    results = {}
    
    for name, url in dashboards:
        print(f"\n🔗 Test {name}...")
        print(f"   URL: {url}")
        
        try:
            response = requests.get(url, timeout=15, allow_redirects=False)
            
            if response.status_code == 200:
                content_length = len(response.content)
                print(f"   ✅ Accessible")
                print(f"   📄 Taille: {content_length} bytes")
                
                # Vérifier le contenu
                content = response.text.lower()
                if name == "ERP Dashboard":
                    if "null" in content or content_length < 100:
                        print(f"   ⚠️ Contenu suspect (trop court ou null)")
                        results[name] = "partial"
                    else:
                        print(f"   ✅ Contenu valide")
                        results[name] = "success"
                elif name == "Superset Dashboard":
                    if "chart" in content and "dashboard" in content:
                        print(f"   ✅ Dashboard avec graphiques détecté")
                        results[name] = "success"
                    else:
                        print(f"   ⚠️ Pas de graphiques détectés")
                        results[name] = "partial"
                elif name == "IFC Viewer":
                    if "visualiseur" in content or "viewer" in content:
                        print(f"   ✅ Viewer IFC détecté")
                        results[name] = "success"
                    else:
                        print(f"   ⚠️ Contenu viewer non détecté")
                        results[name] = "partial"
                else:
                    results[name] = "success"
                    
            elif response.status_code in [301, 302, 307, 308]:
                print(f"   ⚠️ Redirection ({response.status_code})")
                print(f"   🔗 Vers: {response.headers.get('Location', 'Unknown')}")
                results[name] = "redirect"
            else:
                print(f"   ❌ Erreur {response.status_code}")
                results[name] = "error"
                
        except requests.exceptions.Timeout:
            print(f"   ❌ Timeout")
            results[name] = "timeout"
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connexion refusée")
            results[name] = "connection_error"
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
            results[name] = "error"
    
    return results

def test_orchestration_and_dashboards():
    """Teste l'orchestration puis les dashboards"""
    
    print("\n🎭 Test Orchestration + Dashboards")
    print("=" * 50)
    
    base_url = "http://localhost:8010"
    test_project = "basic2"
    
    # Lancer l'orchestration
    print("1. 🎭 Lancement de l'orchestration...")
    try:
        response = requests.post(
            f"{base_url}/bi/launch-real-orchestration",
            data={"project_id": test_project},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            successful_connectors = data.get('successful_connectors', 0)
            print(f"   ✅ Orchestration réussie: {successful_connectors}/4 connecteurs")
        else:
            print(f"   ❌ Erreur orchestration: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False
    
    # Attendre un peu
    print("2. ⏳ Attente (2 secondes)...")
    time.sleep(2)
    
    # Tester les dashboards
    print("3. 📊 Test des dashboards...")
    results = test_dashboard_urls()
    
    return results

def show_dashboard_preview(results):
    """Affiche un aperçu des dashboards"""
    
    print("\n📊 Aperçu des Dashboards")
    print("=" * 50)
    
    for name, status in results.items():
        if status == "success":
            print(f"✅ {name}: Fonctionnel")
        elif status == "partial":
            print(f"⚠️ {name}: Partiellement fonctionnel")
        elif status == "redirect":
            print(f"🔄 {name}: Redirection")
        else:
            print(f"❌ {name}: Problème ({status})")
    
    # Compter les succès
    successful = sum(1 for status in results.values() if status in ["success", "partial"])
    total = len(results)
    
    print(f"\n📈 Score: {successful}/{total} dashboards fonctionnels")
    
    if successful >= 3:
        print("🎉 EXCELLENT! La plupart des dashboards fonctionnent")
    elif successful >= 2:
        print("👍 BON! Quelques dashboards à corriger")
    else:
        print("⚠️ Plusieurs dashboards ont des problèmes")

def show_usage_instructions():
    """Affiche les instructions d'utilisation"""
    
    print("\n🎯 Instructions d'Utilisation")
    print("=" * 50)
    
    print("\n1. 🌐 Ouvrir le dashboard principal:")
    print("   http://localhost:8010/bi_dashboard_dynamic.html")
    
    print("\n2. 🎭 Lancer l'orchestration:")
    print("   - Cliquez sur 'Orchestrer' sur le projet 'basic2'")
    print("   - Le popup s'ouvrira automatiquement")
    
    print("\n3. 📊 Accéder aux dashboards:")
    print("   - ERP Dashboard: http://localhost:8010/erp-dashboard?project_id=basic2")
    print("   - Superset: http://localhost:8010/superset-dashboard?project=basic2")
    print("   - IFC Viewer: http://localhost:8010/ifc-viewer?project_id=basic2")
    print("   - n8n: http://localhost:5678")
    
    print("\n4. 🎨 Fonctionnalités disponibles:")
    print("   - Dashboards générés automatiquement")
    print("   - Charts et KPIs en temps réel")
    print("   - Analyse IFC intégrée")
    print("   - Gestion de projet ERP")
    
    print("\n💡 Note: Les dashboards sont maintenant optimisés")
    print("   - Plus d'erreur 'null' dans l'ERP")
    print("   - Plus de redirections infinies dans Superset")
    print("   - Contenu HTML optimisé pour éviter Content-Length")

def main():
    print("🔧 BIMEX - Test des Dashboards Corrigés")
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Test 1: URLs directes
    print("Phase 1: Test direct des URLs")
    results_direct = test_dashboard_urls()
    
    # Test 2: Orchestration + Dashboards
    print("\nPhase 2: Test avec orchestration")
    results_orchestration = test_orchestration_and_dashboards()
    
    # Affichage des résultats
    show_dashboard_preview(results_orchestration or results_direct)
    
    # Instructions
    show_usage_instructions()
    
    # Résumé final
    working_dashboards = sum(1 for status in (results_orchestration or results_direct).values() 
                           if status in ["success", "partial"])
    
    print(f"\n🎉 RÉSUMÉ FINAL:")
    print(f"   📊 Dashboards fonctionnels: {working_dashboards}/4")
    
    if working_dashboards >= 3:
        print(f"   ✅ SYSTÈME OPÉRATIONNEL!")
        print(f"   🚀 Prêt pour la démonstration")
    else:
        print(f"   ⚠️ Quelques corrections encore nécessaires")

if __name__ == "__main__":
    main()
