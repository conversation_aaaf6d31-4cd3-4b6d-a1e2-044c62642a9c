#!/usr/bin/env python3
"""
🔧 Script pour corriger les problèmes de connecteurs BIMEX
"""

import requests
import json
from pathlib import Path

def check_and_fix_config():
    """Vérifie et corrige la configuration si nécessaire"""
    print("🔧 Vérification de la configuration")
    print("=" * 40)
    
    config_path = Path("backend/bi_config.json")
    
    if not config_path.exists():
        print("❌ Fichier bi_config.json non trouvé")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ Configuration lue avec succès")
        
        # Vérifier que tous les connecteurs sont actifs
        connectors = config.get('connectors', [])
        all_active = True
        
        for connector in connectors:
            name = connector.get('name', 'Unknown')
            active = connector.get('active', False)
            
            if not active:
                print(f"⚠️ {name} est inactif - activation...")
                connector['active'] = True
                all_active = False
        
        if not all_active:
            # Sauvegarder la configuration corrigée
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print("✅ Configuration corrigée et sauvegardée")
        else:
            print("✅ Tous les connecteurs sont déjà actifs")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_server_status():
    """Teste le statut du serveur"""
    print("\n📊 Test du statut du serveur")
    print("=" * 40)
    
    try:
        response = requests.get("http://localhost:8010/bi/status", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Serveur accessible")
            
            modules = data.get('modules', {})
            print(f"📦 Modules chargés:")
            for module, status in modules.items():
                print(f"   {'✅' if status else '❌'} {module}")
            
            # Vérifier spécifiquement real_connectors
            real_connectors = modules.get('real_connectors', False)
            if real_connectors:
                print("✅ Module real_connectors chargé")
                return True
            else:
                print("❌ Module real_connectors non chargé")
                print("💡 Redémarrage du serveur requis")
                return False
        else:
            print(f"❌ Erreur serveur: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Serveur non accessible: {e}")
        return False

def force_reload_test():
    """Force un test de rechargement des connecteurs"""
    print("\n🔄 Test de rechargement des connecteurs")
    print("=" * 40)
    
    try:
        # Tester l'endpoint de santé des connecteurs
        response = requests.get("http://localhost:8010/bi/connectors-health", timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            
            summary = data.get('summary', {})
            print(f"📊 Total: {summary.get('total_connectors', 0)}")
            print(f"📊 Actifs: {summary.get('active_connectors', 0)}")
            
            detailed = data.get('detailed_status', {})
            for name, status in detailed.items():
                active = status.get('active', False)
                online = status.get('online', False)
                
                status_text = "✅ Actif" if active else "❌ Inactif"
                online_text = "🟢 En ligne" if online else "🔴 Hors ligne"
                
                print(f"   {name}: {status_text}, {online_text}")
                
                if status.get('error'):
                    print(f"      Erreur: {status['error']}")
            
            # Si tous sont actifs, le problème est résolu
            if summary.get('active_connectors', 0) == summary.get('total_connectors', 0):
                print("✅ Tous les connecteurs sont maintenant actifs!")
                return True
            else:
                print("⚠️ Certains connecteurs restent inactifs")
                return False
        else:
            print(f"❌ Erreur API: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def quick_orchestration_test():
    """Test rapide d'orchestration"""
    print("\n🎭 Test rapide d'orchestration")
    print("=" * 40)
    
    try:
        response = requests.post(
            "http://localhost:8010/bi/launch-real-orchestration",
            data={"project_id": "basic2"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            success = data.get('success', False)
            successful = data.get('successful_connectors', 0)
            total = data.get('connectors_executed', 0)
            
            print(f"📊 Résultat: {'✅ Succès' if success else '⚠️ Partiel'}")
            print(f"📊 Connecteurs réussis: {successful}/{total}")
            
            if data.get('errors'):
                print(f"❌ Erreurs: {', '.join(data['errors'])}")
            
            return success
        else:
            print(f"❌ Erreur: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def show_next_steps():
    """Affiche les prochaines étapes"""
    print("\n" + "=" * 60)
    print("🎯 PROCHAINES ÉTAPES")
    print("=" * 60)
    
    print("\n1. 🔄 Si le module real_connectors n'est pas chargé:")
    print("   - Arrêtez le serveur BIMEX (Ctrl+C)")
    print("   - Redémarrez avec: cd backend && python main.py")
    print("   - Vérifiez les logs de démarrage")
    
    print("\n2. 🌐 Tester le dashboard:")
    print("   - Ouvrez: http://localhost:8010/bi_dashboard_dynamic.html")
    print("   - Utilisez les boutons de test des connecteurs")
    print("   - Cliquez sur 'Orchestrer' pour un test complet")
    
    print("\n3. 🔍 Vérifier les logs:")
    print("   - Regardez la console du serveur BIMEX")
    print("   - Cherchez: '🔗 Gestionnaire de Connecteurs Réels chargé'")
    print("   - Cherchez: '✅ Configuration chargée: 4 connecteurs'")
    
    print("\n4. 🚀 Services optionnels:")
    print("   - Les connecteurs fonctionnent en mode simulation")
    print("   - Pour des actions réelles, démarrez les services Docker")

def main():
    print("🔧 BIMEX - Correction des Connecteurs")
    print(f"⏰ {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Étape 1: Vérifier et corriger la configuration
    config_ok = check_and_fix_config()
    
    # Étape 2: Tester le statut du serveur
    server_ok = test_server_status()
    
    if server_ok:
        # Étape 3: Tester le rechargement des connecteurs
        connectors_ok = force_reload_test()
        
        if connectors_ok:
            # Étape 4: Test d'orchestration rapide
            orchestration_ok = quick_orchestration_test()
            
            if orchestration_ok:
                print("\n🎉 TOUS LES TESTS RÉUSSIS!")
                print("✅ Configuration OK")
                print("✅ Serveur OK")
                print("✅ Connecteurs OK")
                print("✅ Orchestration OK")
                print("\n🚀 Le système est prêt à l'emploi!")
            else:
                print("\n⚠️ Orchestration partiellement échouée")
                print("💡 Certains services externes peuvent être indisponibles")
        else:
            print("\n❌ Problème avec les connecteurs")
    else:
        print("\n❌ Problème avec le serveur")
    
    # Afficher les prochaines étapes
    show_next_steps()

if __name__ == "__main__":
    main()
