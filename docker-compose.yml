services:
  superset:
    image: apache/superset
    container_name: superset
    ports:
      - "8088:8088"
    environment:
      - SUPERSET_ENV=production
      - SUPERSET_SECRET_KEY=mysecretkey
      - ADMIN_USERNAME=admin
      - ADMIN_FIRST_NAME=Admin
      - ADMIN_LAST_NAME=User
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=admin
    volumes:
      - superset_home:/app/superset_home
    command: >
      /bin/sh -c "
        superset db upgrade &&
        superset fab create-admin --username admin --firstname Admin --lastname User --email <EMAIL> --password admin &&
        superset init &&
        superset run -h 0.0.0.0 -p 8088"
    depends_on:
      redis:
        condition: service_started
    restart: unless-stopped

  mariadb:
    image: mariadb:10.6
    container_name: mariadb
    environment:
      - MYSQL_ROOT_PASSWORD=root
    volumes:
      - mariadb_data:/var/lib/mysql
    healthcheck:
      test: ["<PERSON><PERSON>", "mysqladmin", "ping", "-h", "localhost"]
      interval: 5s
      timeout: 10s
      retries: 10
    restart: unless-stopped

  redis:
    image: redis:alpine
    container_name: redis
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  erpnext:
    image: frappe/erpnext:v15.17.0
    container_name: erpnext
    ports:
      - "8000:8000"
    environment:
      SITE_NAME: site.localhost
      DB_ROOT_USER: root
      MYSQL_ROOT_PASSWORD: root
      ADMIN_PASSWORD: admin
      INSTALL_APPS: erpnext
    volumes:
      - erpnext_sites:/home/<USER>/frappe-bench/sites
    depends_on:
      mariadb:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  ifc-viewer:  # Nom cohérent avec votre image
    image: ifc-viewer  # Utilise l'image locale que vous avez buildée
    container_name: ifc-viewer
    ports:
      - "3000:80"  # NGINX écoute sur le port 80 dans le conteneur
    volumes:
      - ./web-ifc-viewer:/usr/share/nginx/html:ro  # Montage en lecture seule
    restart: unless-stopped

  n8n:
    image: n8nio/n8n
    container_name: n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin
    volumes:
      - n8n_data:/home/<USER>/.n8n
    restart: unless-stopped

volumes:
  superset_home:
  erpnext_sites:
  mariadb_data:
  n8n_data: