"""
🚀 BIMEX - Générateur Automatique de Workflows n8n Intelligents
Génère des workflows n8n personnalisés basés sur le type de projet BIM et sa complexité
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class BuildingType(Enum):
    RESIDENTIAL = "residential"
    COMMERCIAL = "commercial"
    INDUSTRIAL = "industrial"
    HEALTHCARE = "healthcare"
    EDUCATIONAL = "educational"
    MIXED_USE = "mixed_use"
    INFRASTRUCTURE = "infrastructure"

class ComplexityLevel(Enum):
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    VERY_COMPLEX = "very_complex"

@dataclass
class WorkflowTemplate:
    name: str
    description: str
    building_types: List[BuildingType]
    complexity_levels: List[ComplexityLevel]
    nodes: List[Dict[str, Any]]
    schedule: Optional[str] = None
    priority: int = 1

class IntelligentWorkflowGenerator:
    """🧠 Générateur intelligent de workflows n8n personnalisés"""
    
    def __init__(self):
        self.templates = self._initialize_templates()
        self.base_url = "http://localhost:8000"
        self.n8n_url = "http://localhost:5678"
    
    def _initialize_templates(self) -> List[WorkflowTemplate]:
        """Initialise les templates de workflows intelligents"""
        return [
            # Workflow d'analyse quotidienne
            WorkflowTemplate(
                name="Analyse_BIM_Quotidienne",
                description="Analyse automatique quotidienne des données BIM",
                building_types=[BuildingType.RESIDENTIAL, BuildingType.COMMERCIAL],
                complexity_levels=[ComplexityLevel.SIMPLE, ComplexityLevel.MEDIUM],
                schedule="0 6 * * *",  # Tous les jours à 6h
                nodes=[
                    {
                        "name": "Déclencheur Quotidien",
                        "type": "n8n-nodes-base.cron",
                        "parameters": {
                            "triggerTimes": {
                                "mode": "everyDay",
                                "hour": 6,
                                "minute": 0
                            }
                        },
                        "position": [250, 300]
                    },
                    {
                        "name": "Analyser Projet BIM",
                        "type": "n8n-nodes-base.httpRequest",
                        "parameters": {
                            "url": "http://localhost:8000/analyze-comprehensive-project/{{ $json.project_id }}",
                            "method": "GET",
                            "options": {
                                "timeout": 300000
                            }
                        },
                        "position": [450, 300]
                    },
                    {
                        "name": "Export vers Superset",
                        "type": "n8n-nodes-base.httpRequest",
                        "parameters": {
                            "url": "http://localhost:8000/bi/export-superset",
                            "method": "POST",
                            "body": {
                                "project_id": "={{ $json.project_id }}",
                                "data": "={{ $json }}"
                            }
                        },
                        "position": [650, 300]
                    },
                    {
                        "name": "Notification Équipe",
                        "type": "n8n-nodes-base.emailSend",
                        "parameters": {
                            "subject": "Rapport BIM Quotidien - {{ $json.project_id }}",
                            "text": "L'analyse quotidienne du projet {{ $json.project_id }} est terminée.\n\nRésultats:\n- Éléments analysés: {{ $json.performance_kpis.total_elements }}\n- Surface totale: {{ $json.building_metrics.total_floor_area }} m²"
                        },
                        "position": [850, 300]
                    }
                ]
            ),
            
            # Workflow de synchronisation ERPNext
            WorkflowTemplate(
                name="Sync_ERPNext_Temps_Reel",
                description="Synchronisation temps réel avec ERPNext",
                building_types=[BuildingType.COMMERCIAL, BuildingType.INDUSTRIAL],
                complexity_levels=[ComplexityLevel.MEDIUM, ComplexityLevel.COMPLEX],
                nodes=[
                    {
                        "name": "Webhook BIM Update",
                        "type": "n8n-nodes-base.webhook",
                        "parameters": {
                            "path": "bim-update",
                            "httpMethod": "POST"
                        },
                        "position": [250, 300]
                    },
                    {
                        "name": "Extraire Données Coûts",
                        "type": "n8n-nodes-base.function",
                        "parameters": {
                            "functionCode": """
                                const bimData = items[0].json;
                                const costData = {
                                    project_id: bimData.project_id,
                                    total_cost: bimData.cost_metrics?.total_estimated_cost || 0,
                                    materials_cost: bimData.cost_metrics?.materials_cost || 0,
                                    labor_cost: bimData.cost_metrics?.labor_cost || 0,
                                    updated_at: new Date().toISOString()
                                };
                                return [{ json: costData }];
                            """
                        },
                        "position": [450, 300]
                    },
                    {
                        "name": "Sync ERPNext",
                        "type": "n8n-nodes-base.httpRequest",
                        "parameters": {
                            "url": "http://localhost:8000/bi/sync-erp",
                            "method": "POST",
                            "body": {
                                "project_id": "={{ $json.project_id }}",
                                "cost_data": "={{ $json }}"
                            }
                        },
                        "position": [650, 300]
                    }
                ]
            ),
            
            # Workflow d'alertes intelligentes
            WorkflowTemplate(
                name="Alertes_Intelligentes_BIM",
                description="Détection d'anomalies et alertes automatiques",
                building_types=[BuildingType.HEALTHCARE, BuildingType.EDUCATIONAL],
                complexity_levels=[ComplexityLevel.COMPLEX, ComplexityLevel.VERY_COMPLEX],
                schedule="0 */2 * * *",  # Toutes les 2 heures
                nodes=[
                    {
                        "name": "Vérification Périodique",
                        "type": "n8n-nodes-base.cron",
                        "parameters": {
                            "triggerTimes": {
                                "mode": "everyHour",
                                "hour": 2
                            }
                        },
                        "position": [250, 300]
                    },
                    {
                        "name": "Analyser Anomalies",
                        "type": "n8n-nodes-base.function",
                        "parameters": {
                            "functionCode": """
                                // Logique de détection d'anomalies
                                const data = items[0].json;
                                const anomalies = [];

                                // Vérifier les seuils critiques
                                if (data.performance_kpis?.total_elements > 50000) {
                                    anomalies.push({
                                        type: 'complexity_warning',
                                        message: 'Projet très complexe détecté',
                                        severity: 'high'
                                    });
                                }

                                if (data.building_metrics?.total_floor_area > 10000) {
                                    anomalies.push({
                                        type: 'size_warning',
                                        message: 'Bâtiment de grande taille',
                                        severity: 'medium'
                                    });
                                }

                                return anomalies.length > 0 ? [{ json: { anomalies, project_id: data.project_id } }] : [];
                            """
                        },
                        "position": [450, 300]
                    },
                    {
                        "name": "Alerte Urgente",
                        "type": "n8n-nodes-base.emailSend",
                        "parameters": {
                            "subject": "🚨 ALERTE BIM - {{ $json.project_id }}",
                            "text": "Anomalies détectées dans le projet {{ $json.project_id }}:\n\n{{ $json.anomalies }}"
                        },
                        "position": [650, 300]
                    }
                ]
            ),
            
            # Workflow de rapport hebdomadaire
            WorkflowTemplate(
                name="Rapport_Hebdomadaire_Executif",
                description="Rapport exécutif hebdomadaire avec métriques avancées",
                building_types=list(BuildingType),  # Tous les types
                complexity_levels=list(ComplexityLevel),  # Tous les niveaux
                schedule="0 8 * * 1",  # Tous les lundis à 8h
                nodes=[
                    {
                        "name": "Déclencheur Hebdomadaire",
                        "type": "n8n-nodes-base.cron",
                        "parameters": {
                            "triggerTimes": {
                                "mode": "everyWeek",
                                "weekday": 1,
                                "hour": 8,
                                "minute": 0
                            }
                        },
                        "position": [250, 300]
                    },
                    {
                        "name": "Collecter Données Semaine",
                        "type": "n8n-nodes-base.function",
                        "parameters": {
                            "functionCode": """
                                // Collecter les données de la semaine passée
                                const endDate = new Date();
                                const startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);

                                return [{
                                    json: {
                                        period: {
                                            start: startDate.toISOString(),
                                            end: endDate.toISOString()
                                        },
                                        project_id: items[0].json.project_id
                                    }
                                }];
                            """
                        },
                        "position": [450, 300]
                    },
                    {
                        "name": "Générer Rapport PDF",
                        "type": "n8n-nodes-base.httpRequest",
                        "parameters": {
                            "url": "http://localhost:8000/generate-weekly-report",
                            "method": "POST",
                            "body": "={{ $json }}"
                        },
                        "position": [650, 300]
                    },
                    {
                        "name": "Envoyer Rapport Direction",
                        "type": "n8n-nodes-base.emailSend",
                        "parameters": {
                            "subject": "📊 Rapport Hebdomadaire BIM - Semaine {{ $now.format('WW/YYYY') }}",
                            "text": "Veuillez trouver en pièce jointe le rapport hebdomadaire du projet {{ $json.project_id }}.",
                            "attachments": "={{ $json.pdf_path }}"
                        },
                        "position": [850, 300]
                    }
                ]
            )
        ]
    
    def analyze_project_requirements(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyse les exigences du projet pour déterminer les workflows appropriés"""
        
        # Déterminer le type de bâtiment
        building_type = self._classify_building_type(project_data)
        
        # Déterminer le niveau de complexité
        complexity = self._assess_complexity(project_data)
        
        # Analyser les besoins spécifiques
        requirements = {
            'building_type': building_type,
            'complexity_level': complexity,
            'total_elements': project_data.get('performance_kpis', {}).get('total_elements', 0),
            'floor_area': project_data.get('building_metrics', {}).get('total_floor_area', 0),
            'has_cost_data': 'cost_metrics' in project_data,
            'requires_compliance': building_type in [BuildingType.HEALTHCARE, BuildingType.EDUCATIONAL],
            'needs_frequent_updates': complexity in [ComplexityLevel.COMPLEX, ComplexityLevel.VERY_COMPLEX]
        }
        
        return requirements
    
    def _classify_building_type(self, project_data: Dict[str, Any]) -> BuildingType:
        """Classifie le type de bâtiment basé sur les données du projet"""
        
        classification = project_data.get('building_classification', {})
        building_type_str = classification.get('building_type', '').lower()
        
        # Mapping des types de bâtiments
        type_mapping = {
            'residential': BuildingType.RESIDENTIAL,
            'house': BuildingType.RESIDENTIAL,
            'apartment': BuildingType.RESIDENTIAL,
            'commercial': BuildingType.COMMERCIAL,
            'office': BuildingType.COMMERCIAL,
            'retail': BuildingType.COMMERCIAL,
            'industrial': BuildingType.INDUSTRIAL,
            'factory': BuildingType.INDUSTRIAL,
            'warehouse': BuildingType.INDUSTRIAL,
            'hospital': BuildingType.HEALTHCARE,
            'clinic': BuildingType.HEALTHCARE,
            'healthcare': BuildingType.HEALTHCARE,
            'school': BuildingType.EDUCATIONAL,
            'university': BuildingType.EDUCATIONAL,
            'educational': BuildingType.EDUCATIONAL
        }
        
        for keyword, btype in type_mapping.items():
            if keyword in building_type_str:
                return btype
        
        return BuildingType.MIXED_USE  # Par défaut
    
    def _assess_complexity(self, project_data: Dict[str, Any]) -> ComplexityLevel:
        """Évalue le niveau de complexité du projet"""
        
        total_elements = project_data.get('performance_kpis', {}).get('total_elements', 0)
        floor_area = project_data.get('building_metrics', {}).get('total_floor_area', 0)
        num_storeys = project_data.get('building_metrics', {}).get('number_of_storeys', 1)
        
        # Score de complexité basé sur plusieurs facteurs
        complexity_score = 0
        
        # Facteur éléments
        if total_elements > 100000:
            complexity_score += 4
        elif total_elements > 50000:
            complexity_score += 3
        elif total_elements > 10000:
            complexity_score += 2
        elif total_elements > 1000:
            complexity_score += 1
        
        # Facteur surface
        if floor_area > 50000:
            complexity_score += 3
        elif floor_area > 10000:
            complexity_score += 2
        elif floor_area > 1000:
            complexity_score += 1
        
        # Facteur étages
        if num_storeys > 20:
            complexity_score += 3
        elif num_storeys > 10:
            complexity_score += 2
        elif num_storeys > 5:
            complexity_score += 1
        
        # Classification finale
        if complexity_score >= 8:
            return ComplexityLevel.VERY_COMPLEX
        elif complexity_score >= 5:
            return ComplexityLevel.COMPLEX
        elif complexity_score >= 2:
            return ComplexityLevel.MEDIUM
        else:
            return ComplexityLevel.SIMPLE
    
    def generate_workflows(self, project_id: str, project_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Génère les workflows appropriés pour un projet donné"""
        
        requirements = self.analyze_project_requirements(project_data)
        selected_workflows = []
        
        for template in self.templates:
            # Vérifier si le template correspond aux exigences
            if (requirements['building_type'] in template.building_types and
                requirements['complexity_level'] in template.complexity_levels):
                
                # Personnaliser le workflow pour ce projet
                workflow = self._customize_workflow(template, project_id, requirements)
                selected_workflows.append(workflow)
        
        return selected_workflows
    
    def _customize_workflow(self, template: WorkflowTemplate, project_id: str, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Personnalise un template de workflow pour un projet spécifique"""
        
        workflow = {
            "name": f"{template.name}_{project_id}",
            "active": True,
            "nodes": [],
            "connections": {},
            "settings": {
                "executionOrder": "v1"
            },
            "staticData": {},
            "meta": {
                "templateId": template.name,
                "projectId": project_id,
                "generatedAt": datetime.now().isoformat(),
                "requirements": requirements
            }
        }
        
        # Personnaliser les nœuds
        for i, node_template in enumerate(template.nodes):
            node = node_template.copy()
            node["id"] = str(uuid.uuid4())
            
            # Injecter l'ID du projet dans les paramètres
            if "parameters" in node:
                node["parameters"] = self._inject_project_variables(
                    node["parameters"], project_id, requirements
                )
            
            workflow["nodes"].append(node)
            
            # Créer les connexions séquentielles
            if i > 0:
                prev_node_id = workflow["nodes"][i-1]["id"]
                current_node_id = node["id"]
                
                if prev_node_id not in workflow["connections"]:
                    workflow["connections"][prev_node_id] = {"main": [[]]}
                
                workflow["connections"][prev_node_id]["main"][0].append({
                    "node": node["name"],
                    "type": "main",
                    "index": 0
                })
        
        return workflow
    
    def _inject_project_variables(self, parameters: Dict[str, Any], project_id: str, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Injecte les variables du projet dans les paramètres du nœud"""
        
        # Remplacer les placeholders dans les paramètres
        params_str = json.dumps(parameters)
        params_str = params_str.replace("{{ $json.project_id }}", project_id)
        params_str = params_str.replace("{{ project_id }}", project_id)
        
        # Ajouter des paramètres spécifiques selon les exigences
        if requirements.get('needs_frequent_updates'):
            # Réduire les intervalles pour les projets complexes
            if "triggerTimes" in parameters:
                if "hour" in parameters["triggerTimes"]:
                    parameters["triggerTimes"]["hour"] = 2  # Toutes les 2 heures
        
        return json.loads(params_str)
    
    async def deploy_workflows(self, workflows: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Déploie les workflows générés sur n8n"""
        
        deployment_results = {
            "success": True,
            "deployed_workflows": [],
            "errors": []
        }
        
        for workflow in workflows:
            try:
                # Simuler le déploiement (dans un vrai environnement, on utiliserait l'API n8n)
                workflow_id = str(uuid.uuid4())
                
                deployment_results["deployed_workflows"].append({
                    "id": workflow_id,
                    "name": workflow["name"],
                    "status": "active",
                    "deployed_at": datetime.now().isoformat()
                })
                
            except Exception as e:
                deployment_results["errors"].append({
                    "workflow_name": workflow["name"],
                    "error": str(e)
                })
                deployment_results["success"] = False
        
        return deployment_results

# Instance globale du générateur
workflow_generator = IntelligentWorkflowGenerator()
