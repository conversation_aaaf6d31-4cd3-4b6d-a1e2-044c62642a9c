#!/usr/bin/env python3
"""
🔍 Debug des connecteurs BIMEX
"""

import sys
import asyncio
import json
from pathlib import Path

# Ajouter le répertoire backend au path
sys.path.append('backend')

async def debug_connectors():
    """Debug direct du gestionnaire de connecteurs"""
    
    print("🔍 Debug du gestionnaire de connecteurs")
    print("=" * 50)
    
    try:
        from real_connectors_manager import RealConnectorsManager
        
        # Créer une instance du gestionnaire
        manager = RealConnectorsManager()
        
        print(f"📁 Chemin de configuration: {manager.config_path}")
        print(f"📁 Chemin absolu: {manager.config_path.absolute()}")
        print(f"📁 Fichier existe: {manager.config_path.exists()}")
        
        # Afficher la configuration chargée
        print(f"\n📋 Configuration chargée:")
        print(f"   Nombre de connecteurs: {len(manager.connectors_config)}")
        
        for name, config in manager.connectors_config.items():
            print(f"   - {name}:")
            print(f"     Type: {config.get('type', 'N/A')}")
            print(f"     Endpoint: {config.get('endpoint', 'N/A')}")
            print(f"     Actif: {config.get('active', False)}")
        
        # Tester la santé de chaque connecteur
        print(f"\n🏥 Test de santé des connecteurs:")
        
        for connector_name in manager.connectors_config.keys():
            print(f"\n🔗 Test de {connector_name}:")
            try:
                status = await manager.check_connector_health(connector_name)
                print(f"   Nom: {status.name}")
                print(f"   Type: {status.type}")
                print(f"   Endpoint: {status.endpoint}")
                print(f"   Actif: {status.active}")
                print(f"   En ligne: {status.online}")
                print(f"   Temps de réponse: {status.response_time:.3f}s")
                if status.error:
                    print(f"   Erreur: {status.error}")
                    
            except Exception as e:
                print(f"   ❌ Erreur: {e}")
        
        # Tester le résumé
        print(f"\n📊 Résumé des connecteurs:")
        summary = manager.get_connectors_summary()
        print(f"   Total: {summary['total_connectors']}")
        print(f"   Actifs: {summary['active_connectors']}")
        
        for name, info in summary['connectors'].items():
            print(f"   - {name}: {'✅' if info['active'] else '❌'} {info['type']}")
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def debug_config_file():
    """Debug direct du fichier de configuration"""
    
    print("\n📋 Debug du fichier de configuration")
    print("=" * 50)
    
    config_paths = [
        Path("backend/bi_config.json"),
        Path("bi_config.json"),
        Path("../backend/bi_config.json"),
        Path("./backend/bi_config.json")
    ]
    
    for path in config_paths:
        print(f"\n📁 Test du chemin: {path}")
        print(f"   Absolu: {path.absolute()}")
        print(f"   Existe: {path.exists()}")
        
        if path.exists():
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                print(f"   ✅ Fichier lu avec succès")
                print(f"   📊 Connecteurs: {len(config.get('connectors', []))}")
                
                for conn in config.get('connectors', []):
                    name = conn.get('name', 'Unknown')
                    active = conn.get('active', False)
                    print(f"     - {name}: {'✅ Actif' if active else '❌ Inactif'}")
                
                return config
                
            except Exception as e:
                print(f"   ❌ Erreur lecture: {e}")
    
    return None

async def test_api_endpoints():
    """Test des endpoints API"""
    
    print("\n🌐 Test des endpoints API")
    print("=" * 50)
    
    import aiohttp
    
    base_url = "http://localhost:8010"
    
    endpoints = [
        "/bi/status",
        "/bi/connectors-health"
    ]
    
    async with aiohttp.ClientSession() as session:
        for endpoint in endpoints:
            print(f"\n🔗 Test de {endpoint}")
            try:
                async with session.get(f"{base_url}{endpoint}", timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"   ✅ Status: {response.status}")
                        
                        if endpoint == "/bi/status":
                            modules = data.get('modules', {})
                            real_connectors = modules.get('real_connectors', False)
                            print(f"   🔗 Module real_connectors: {'✅' if real_connectors else '❌'}")
                            
                            connectors = data.get('connectors', [])
                            print(f"   📊 Connecteurs: {len(connectors)}")
                            
                        elif endpoint == "/bi/connectors-health":
                            summary = data.get('summary', {})
                            print(f"   📊 Total: {summary.get('total_connectors', 0)}")
                            print(f"   📊 Actifs: {summary.get('active_connectors', 0)}")
                            
                    else:
                        print(f"   ❌ Status: {response.status}")
                        text = await response.text()
                        print(f"   Réponse: {text[:200]}...")
                        
            except Exception as e:
                print(f"   ❌ Erreur: {e}")

if __name__ == "__main__":
    print("🔍 BIMEX - Debug des Connecteurs")
    print(f"⏰ {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test 1: Fichier de configuration
    config = debug_config_file()
    
    # Test 2: Gestionnaire de connecteurs
    asyncio.run(debug_connectors())
    
    # Test 3: Endpoints API
    asyncio.run(test_api_endpoints())
    
    print("\n🎉 Debug terminé!")
    print("\n💡 Si les connecteurs apparaissent comme inactifs:")
    print("1. Vérifiez que le fichier bi_config.json est bien lu")
    print("2. Vérifiez que 'active': true dans la configuration")
    print("3. Redémarrez le serveur BIMEX")
    print("4. Testez à nouveau les connecteurs")
