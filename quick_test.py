#!/usr/bin/env python3
"""
🚀 Test rapide des dashboards BIMEX
"""

import requests
import time

def test_server():
    """Test rapide du serveur"""
    print("🚀 Test Rapide BIMEX")
    print("=" * 30)
    
    base_url = "http://localhost:8010"
    
    # Test 1: Serveur accessible
    print("1. 🔍 Test serveur...")
    try:
        response = requests.get(f"{base_url}/bi/status", timeout=5)
        if response.status_code == 200:
            print("   ✅ Serveur accessible")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Serveur non accessible: {e}")
        return False
    
    # Test 2: Dashboards
    print("\n2. 📊 Test dashboards...")
    
    dashboards = [
        ("ERP", f"{base_url}/erp-dashboard?project_id=basic2"),
        ("Superset", f"{base_url}/superset-dashboard?project=basic2"),
        ("IFC", f"{base_url}/ifc-viewer?project_id=basic2")
    ]
    
    working = 0
    for name, url in dashboards:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"   ✅ {name}: OK")
                working += 1
            else:
                print(f"   ❌ {name}: Erreur {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: {e}")
    
    print(f"\n📊 Résultat: {working}/3 dashboards fonctionnels")
    
    if working >= 2:
        print("🎉 SYSTÈME OPÉRATIONNEL!")
        print("\n🎯 Instructions:")
        print("1. Ouvrez: http://localhost:8010/bi_dashboard_dynamic.html")
        print("2. Cliquez sur 'Orchestrer' sur basic2")
        print("3. Le popup s'ouvrira avec les connecteurs")
        print("4. Cliquez sur chaque connecteur pour voir son dashboard")
        return True
    else:
        print("⚠️ Quelques problèmes détectés")
        return False

if __name__ == "__main__":
    test_server()
