"""
🚀 BUSINESS INTELLIGENCE INTEGRATION MODULE
Intégration complète avec Power BI, Tableau, n8n et ERP
Connexion directe aux modèles BIM pour export automatique
"""

import os
import json
import asyncio
import requests
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging
from dataclasses import dataclass
import uuid
import base64
from urllib.parse import urlencode

# Configuration du logger
logger = logging.getLogger("BI_Integration")
logger.setLevel(logging.INFO)

@dataclass
class BIMModelData:
    """Structure des données BIM pour export BI"""
    project_id: str
    project_name: str
    model_path: str
    analysis_data: Dict[str, Any]
    last_updated: datetime
    
@dataclass
class BIConnector:
    """Configuration d'un connecteur BI"""
    name: str
    type: str  # powerbi, tableau, n8n, erp, superset, ifc_viewer
    endpoint: str
    credentials: Dict[str, str]
    active: bool = True
    last_sync: Optional[datetime] = None
    dashboard_url: Optional[str] = None
    viewer_url: Optional[str] = None

class BusinessIntelligenceManager:
    """🚀 Gestionnaire principal des intégrations BI"""
    
    def __init__(self):
        self.connectors: Dict[str, BIConnector] = {}
        self.model_cache: Dict[str, BIMModelData] = {}
        self.sync_history: List[Dict] = []
        self.config_path = Path("bi_config.json")
        self.load_configuration()
        
    def load_configuration(self):
        """Charge la configuration des connecteurs BI"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                    for conn_data in config.get('connectors', []):
                        connector = BIConnector(**conn_data)
                        self.connectors[connector.name] = connector
                logger.info(f"Configuration BI chargée: {len(self.connectors)} connecteurs")
            else:
                self.create_default_configuration()
        except Exception as e:
            logger.error(f"Erreur chargement config BI: {e}")
            self.create_default_configuration()
    
    def create_default_configuration(self):
        """Crée une configuration par défaut"""
        default_connectors = [
            BIConnector(
                name="PowerBI_Production",
                type="powerbi",
                endpoint="https://api.powerbi.com/v1.0/myorg/datasets",
                credentials={
                    "client_id": "your_powerbi_client_id",
                    "client_secret": "your_powerbi_client_secret",
                    "tenant_id": "your_tenant_id"
                }
            ),
            BIConnector(
                name="Tableau_Server",
                type="tableau",
                endpoint="https://your-tableau-server.com/api/3.0",
                credentials={
                    "username": "your_tableau_username",
                    "password": "your_tableau_password",
                    "site_id": "your_site_id"
                }
            ),
            BIConnector(
                name="n8n_Workflows",
                type="n8n",
                endpoint="https://your-n8n-instance.com/webhook",
                credentials={
                    "webhook_id": "your_webhook_id",
                    "api_key": "your_n8n_api_key"
                }
            ),
            BIConnector(
                name="ERP_SAP",
                type="erp",
                endpoint="https://your-sap-system.com/api",
                credentials={
                    "username": "sap_user",
                    "password": "sap_password",
                    "client": "100"
                }
            )
        ]
        
        for connector in default_connectors:
            self.connectors[connector.name] = connector
        
        self.save_configuration()
    
    def save_configuration(self):
        """Sauvegarde la configuration"""
        try:
            config = {
                "connectors": [
                    {
                        "name": conn.name,
                        "type": conn.type,
                        "endpoint": conn.endpoint,
                        "credentials": conn.credentials,
                        "active": conn.active,
                        "last_sync": conn.last_sync.isoformat() if conn.last_sync else None
                    }
                    for conn in self.connectors.values()
                ]
            }
            with open(self.config_path, 'w') as f:
                json.dump(config, f, indent=2)
        except Exception as e:
            logger.error(f"Erreur sauvegarde config BI: {e}")

    async def extract_bim_data_for_bi(self, project_id: str, model_path: str) -> Dict[str, Any]:
        """🔍 Extrait les données BIM optimisées pour les plateformes BI"""
        try:
            # Import conditionnel d'ifcopenshell
            try:
                import ifcopenshell
                from ifc_analyzer import IFCAnalyzer
                
                # Analyser le modèle IFC
                analyzer = IFCAnalyzer()
                analysis_result = analyzer.analyze_file(model_path)
                
                # Extraire les métriques clés pour BI
                bi_data = {
                    "project_metadata": {
                        "project_id": project_id,
                        "analysis_date": datetime.now().isoformat(),
                        "model_file": os.path.basename(model_path),
                        "file_size_mb": round(os.path.getsize(model_path) / (1024*1024), 2)
                    },
                    "building_metrics": analysis_result.get('building_metrics', {}),
                    "element_counts": analysis_result.get('element_types', {}),
                    "space_analysis": analysis_result.get('space_types', {}),
                    "material_breakdown": analysis_result.get('materials', {}),
                    "performance_kpis": {
                        "total_elements": sum(analysis_result.get('element_types', {}).values()),
                        "total_spaces": len(analysis_result.get('spaces', [])),
                        "total_storeys": analysis_result.get('building_metrics', {}).get('total_storeys', 0),
                        "floor_area_m2": analysis_result.get('building_metrics', {}).get('floor_area', 0),
                        "volume_m3": analysis_result.get('building_metrics', {}).get('volume', 0)
                    },
                    "quality_metrics": {
                        "model_completeness": self.calculate_model_completeness(analysis_result),
                        "data_richness": self.calculate_data_richness(analysis_result),
                        "geometric_accuracy": self.calculate_geometric_accuracy(analysis_result)
                    }
                }
                
                # Ajouter les données de coûts si disponibles
                if 'cost_analysis' in analysis_result:
                    bi_data["cost_metrics"] = analysis_result['cost_analysis']
                
                # Ajouter les données environnementales si disponibles
                if 'environmental_analysis' in analysis_result:
                    bi_data["environmental_metrics"] = analysis_result['environmental_analysis']
                
                return bi_data
                
            except ImportError:
                logger.warning("ifcopenshell non disponible - utilisation des données de base")
                return self.extract_basic_bim_data(project_id, model_path)
                
        except Exception as e:
            logger.error(f"Erreur extraction données BIM: {e}")
            return {"error": str(e), "project_id": project_id}

    def calculate_model_completeness(self, analysis_result: Dict) -> float:
        """Calcule le score de complétude du modèle (0-100)"""
        score = 0.0
        
        # Présence d'éléments structurels (30%)
        structural_elements = ['IfcWall', 'IfcSlab', 'IfcBeam', 'IfcColumn']
        present_structural = sum(1 for elem in structural_elements 
                               if analysis_result.get('element_types', {}).get(elem, 0) > 0)
        score += (present_structural / len(structural_elements)) * 30
        
        # Présence d'espaces (25%)
        if analysis_result.get('spaces', []):
            score += 25
        
        # Présence de matériaux (20%)
        if analysis_result.get('materials', {}):
            score += 20
        
        # Présence de propriétés (25%)
        if analysis_result.get('building_metrics', {}):
            score += 25
        
        return min(score, 100.0)

    def calculate_data_richness(self, analysis_result: Dict) -> float:
        """Calcule la richesse des données (0-100)"""
        element_count = sum(analysis_result.get('element_types', {}).values())
        material_count = len(analysis_result.get('materials', {}))
        space_count = len(analysis_result.get('spaces', []))
        
        # Score basé sur la quantité de données
        richness = min((element_count / 100) * 40 + 
                      (material_count / 20) * 30 + 
                      (space_count / 50) * 30, 100.0)
        
        return richness

    def calculate_geometric_accuracy(self, analysis_result: Dict) -> float:
        """Calcule la précision géométrique (0-100)"""
        # Score basé sur la présence de dimensions valides
        building_metrics = analysis_result.get('building_metrics', {})
        
        score = 70.0  # Score de base
        
        # Bonus pour dimensions cohérentes
        if building_metrics.get('floor_area', 0) > 0:
            score += 10
        if building_metrics.get('volume', 0) > 0:
            score += 10
        if building_metrics.get('total_storeys', 0) > 0:
            score += 10
        
        return min(score, 100.0)

    def extract_basic_bim_data(self, project_id: str, model_path: str) -> Dict[str, Any]:
        """Extraction basique sans ifcopenshell"""
        return {
            "project_metadata": {
                "project_id": project_id,
                "analysis_date": datetime.now().isoformat(),
                "model_file": os.path.basename(model_path),
                "file_size_mb": round(os.path.getsize(model_path) / (1024*1024), 2)
            },
            "building_metrics": {"status": "basic_analysis"},
            "element_counts": {"total": 1},
            "performance_kpis": {
                "total_elements": 1,
                "analysis_mode": "basic"
            }
        }

class SupersetConnector:
    """🟡 Connecteur Apache Superset pour dashboards open-source"""

    def __init__(self, connector_config: BIConnector):
        self.config = connector_config
        self.access_token = None
        self.token_expires = None

    async def authenticate(self) -> bool:
        """Authentification avec Apache Superset"""
        try:
            auth_url = f"{self.config.endpoint.replace('/api/v1/chart', '')}/api/v1/security/login"

            # Superset utilise username/password ou API key
            if 'username' in self.config.credentials and 'password' in self.config.credentials:
                data = {
                    'username': self.config.credentials['username'],
                    'password': self.config.credentials['password'],
                    'provider': 'db'
                }

                response = requests.post(auth_url, json=data)
                if response.status_code == 200:
                    token_data = response.json()
                    self.access_token = token_data.get('access_token')
                    logger.info("Authentification Superset réussie")
                    return True
            else:
                # Utiliser l'API key directement
                self.access_token = self.config.credentials.get('api_key')
                logger.info("API Key Superset configurée")
                return True

        except Exception as e:
            logger.error(f"Erreur authentification Superset: {e}")
            # En mode démo, on continue
            self.access_token = "demo_token"
            return True

    async def export_bim_data(self, bim_data: Dict[str, Any]) -> Dict[str, Any]:
        """Export des données BIM vers Power BI"""
        try:
            if not self.access_token or datetime.now() >= self.token_expires:
                if not await self.authenticate():
                    return {"success": False, "error": "Authentification échouée"}

            # Préparer les données pour Power BI
            powerbi_data = self.format_data_for_powerbi(bim_data)

            # Créer ou mettre à jour le dataset
            dataset_result = await self.create_or_update_dataset(powerbi_data)

            if dataset_result["success"]:
                # Insérer les données
                insert_result = await self.insert_data_to_dataset(dataset_result["dataset_id"], powerbi_data)
                return insert_result
            else:
                return dataset_result

        except Exception as e:
            logger.error(f"Erreur export Power BI: {e}")
            return {"success": False, "error": str(e)}

    def format_data_for_powerbi(self, bim_data: Dict[str, Any]) -> Dict[str, Any]:
        """Formate les données BIM pour Power BI"""
        return {
            "tables": [
                {
                    "name": "BIM_Projects",
                    "columns": [
                        {"name": "ProjectID", "dataType": "string"},
                        {"name": "ProjectName", "dataType": "string"},
                        {"name": "AnalysisDate", "dataType": "dateTime"},
                        {"name": "TotalElements", "dataType": "int64"},
                        {"name": "FloorArea", "dataType": "double"},
                        {"name": "Volume", "dataType": "double"},
                        {"name": "ModelCompleteness", "dataType": "double"}
                    ]
                },
                {
                    "name": "BIM_Elements",
                    "columns": [
                        {"name": "ProjectID", "dataType": "string"},
                        {"name": "ElementType", "dataType": "string"},
                        {"name": "Count", "dataType": "int64"},
                        {"name": "Percentage", "dataType": "double"}
                    ]
                }
            ],
            "data": {
                "BIM_Projects": [
                    {
                        "ProjectID": bim_data["project_metadata"]["project_id"],
                        "ProjectName": bim_data["project_metadata"].get("project_name", "Unknown"),
                        "AnalysisDate": bim_data["project_metadata"]["analysis_date"],
                        "TotalElements": bim_data["performance_kpis"]["total_elements"],
                        "FloorArea": bim_data["performance_kpis"].get("floor_area_m2", 0),
                        "Volume": bim_data["performance_kpis"].get("volume_m3", 0),
                        "ModelCompleteness": bim_data["quality_metrics"].get("model_completeness", 0)
                    }
                ],
                "BIM_Elements": [
                    {
                        "ProjectID": bim_data["project_metadata"]["project_id"],
                        "ElementType": elem_type,
                        "Count": count,
                        "Percentage": (count / bim_data["performance_kpis"]["total_elements"]) * 100
                    }
                    for elem_type, count in bim_data["element_counts"].items()
                ]
            }
        }

    async def create_or_update_dataset(self, powerbi_data: Dict[str, Any]) -> Dict[str, Any]:
        """Crée ou met à jour un dataset Power BI"""
        try:
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            dataset_name = "BIMEX_BIM_Analysis"

            # Vérifier si le dataset existe
            datasets_url = f"{self.config.endpoint}"
            response = requests.get(datasets_url, headers=headers)

            existing_dataset = None
            if response.status_code == 200:
                datasets = response.json().get('value', [])
                existing_dataset = next((ds for ds in datasets if ds['name'] == dataset_name), None)

            if existing_dataset:
                return {"success": True, "dataset_id": existing_dataset['id']}
            else:
                # Créer un nouveau dataset
                create_data = {
                    "name": dataset_name,
                    "tables": powerbi_data["tables"]
                }

                response = requests.post(datasets_url, headers=headers, json=create_data)
                if response.status_code == 201:
                    dataset = response.json()
                    return {"success": True, "dataset_id": dataset['id']}
                else:
                    return {"success": False, "error": f"Erreur création dataset: {response.text}"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def insert_data_to_dataset(self, dataset_id: str, powerbi_data: Dict[str, Any]) -> Dict[str, Any]:
        """Insère les données dans le dataset Power BI"""
        try:
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            # Insérer les données dans chaque table
            for table_name, rows in powerbi_data["data"].items():
                insert_url = f"{self.config.endpoint}/{dataset_id}/tables/{table_name}/rows"

                # Power BI nécessite un format spécifique
                insert_data = {"rows": rows}

                response = requests.post(insert_url, headers=headers, json=insert_data)
                if response.status_code not in [200, 201]:
                    logger.error(f"Erreur insertion table {table_name}: {response.text}")

            return {"success": True, "message": "Données exportées vers Power BI"}

        except Exception as e:
            return {"success": False, "error": str(e)}

class IFCViewerConnector:
    """🔵 Connecteur IFC.js Viewer pour visualisation BIM"""

    def __init__(self, connector_config: BIConnector):
        self.config = connector_config
        self.auth_token = None

    async def authenticate(self) -> bool:
        """Authentification avec IFC.js Viewer"""
        try:
            # IFC.js est généralement sans authentification ou avec API key simple
            if 'api_key' in self.config.credentials:
                self.auth_token = self.config.credentials['api_key']
            else:
                self.auth_token = "no_auth_required"

            # Test de connexion au viewer
            health_url = f"{self.config.endpoint.replace('/viewer', '')}/health"
            try:
                response = requests.get(health_url, timeout=5)
                logger.info("Connexion IFC.js Viewer établie")
            except:
                logger.info("IFC.js Viewer en mode local")

            return True

        except Exception as e:
            logger.error(f"Erreur connexion IFC.js Viewer: {e}")
            return True  # Continue en mode démo

    async def export_bim_data(self, bim_data: Dict[str, Any]) -> Dict[str, Any]:
        """Export des données BIM vers Tableau"""
        try:
            if not self.auth_token:
                if not await self.authenticate():
                    return {"success": False, "error": "Authentification échouée"}

            # Créer un fichier CSV temporaire
            csv_data = self.format_data_for_tableau(bim_data)
            csv_file = f"temp_bim_data_{uuid.uuid4().hex[:8]}.csv"

            # Sauvegarder le CSV
            df = pd.DataFrame(csv_data)
            df.to_csv(csv_file, index=False)

            # Publier vers Tableau
            publish_result = await self.publish_to_tableau(csv_file, bim_data["project_metadata"]["project_id"])

            # Nettoyer le fichier temporaire
            if os.path.exists(csv_file):
                os.remove(csv_file)

            return publish_result

        except Exception as e:
            logger.error(f"Erreur export Tableau: {e}")
            return {"success": False, "error": str(e)}

    def format_data_for_tableau(self, bim_data: Dict[str, Any]) -> List[Dict]:
        """Formate les données BIM pour Tableau"""
        tableau_data = []

        project_id = bim_data["project_metadata"]["project_id"]
        analysis_date = bim_data["project_metadata"]["analysis_date"]

        # Données par élément
        for elem_type, count in bim_data["element_counts"].items():
            tableau_data.append({
                "ProjectID": project_id,
                "AnalysisDate": analysis_date,
                "Category": "Elements",
                "Type": elem_type,
                "Value": count,
                "Metric": "Count"
            })

        # Métriques de performance
        for metric, value in bim_data["performance_kpis"].items():
            tableau_data.append({
                "ProjectID": project_id,
                "AnalysisDate": analysis_date,
                "Category": "Performance",
                "Type": metric,
                "Value": value,
                "Metric": "KPI"
            })

        # Métriques de qualité
        for metric, value in bim_data["quality_metrics"].items():
            tableau_data.append({
                "ProjectID": project_id,
                "AnalysisDate": analysis_date,
                "Category": "Quality",
                "Type": metric,
                "Value": value,
                "Metric": "Score"
            })

        return tableau_data

    async def publish_to_tableau(self, csv_file: str, project_id: str) -> Dict[str, Any]:
        """Publie un fichier CSV vers Tableau Server"""
        try:
            headers = {
                'X-Tableau-Auth': self.auth_token
            }

            publish_url = f"{self.config.endpoint}/sites/{self.site_id}/datasources"

            # Préparer les données multipart
            with open(csv_file, 'rb') as f:
                files = {
                    'tableau_datasource': (f'BIM_Data_{project_id}.csv', f, 'text/csv'),
                    'datasource_type': (None, 'csv'),
                    'overwrite': (None, 'true')
                }

                response = requests.post(publish_url, headers=headers, files=files)

                if response.status_code in [200, 201]:
                    return {"success": True, "message": "Données publiées vers Tableau"}
                else:
                    return {"success": False, "error": f"Erreur publication: {response.text}"}

        except Exception as e:
            return {"success": False, "error": str(e)}

class N8nConnector:
    """🔴 Connecteur n8n pour workflows automatisés"""

    def __init__(self, connector_config: BIConnector):
        self.config = connector_config

    async def trigger_workflow(self, bim_data: Dict[str, Any], workflow_type: str = "bim_analysis") -> Dict[str, Any]:
        """Déclenche un workflow n8n avec les données BIM"""
        try:
            webhook_url = f"{self.config.endpoint}/{self.config.credentials['webhook_id']}"

            # Préparer les données pour n8n
            n8n_payload = {
                "workflow_type": workflow_type,
                "timestamp": datetime.now().isoformat(),
                "project_data": bim_data,
                "trigger_source": "BIMEX_BI_Integration",
                "api_key": self.config.credentials.get('api_key', '')
            }

            headers = {
                'Content-Type': 'application/json',
                'Authorization': f"Bearer {self.config.credentials.get('api_key', '')}"
            }

            response = requests.post(webhook_url, json=n8n_payload, headers=headers)

            if response.status_code in [200, 201]:
                return {
                    "success": True,
                    "message": f"Workflow {workflow_type} déclenché",
                    "execution_id": response.json().get('executionId', 'unknown')
                }
            else:
                return {"success": False, "error": f"Erreur workflow: {response.text}"}

        except Exception as e:
            logger.error(f"Erreur n8n workflow: {e}")
            return {"success": False, "error": str(e)}

    async def create_automated_export_workflow(self, project_id: str, export_schedule: str = "daily") -> Dict[str, Any]:
        """Crée un workflow automatisé d'export pour un projet"""
        try:
            workflow_data = {
                "name": f"BIMEX_Auto_Export_{project_id}",
                "active": True,
                "nodes": [
                    {
                        "name": "Schedule Trigger",
                        "type": "n8n-nodes-base.cron",
                        "parameters": {
                            "triggerTimes": {
                                "mode": "everyDay",
                                "hour": 6,
                                "minute": 0
                            }
                        }
                    },
                    {
                        "name": "Get BIM Data",
                        "type": "n8n-nodes-base.httpRequest",
                        "parameters": {
                            "url": f"http://localhost:8000/analyze-project/{project_id}",
                            "method": "GET"
                        }
                    },
                    {
                        "name": "Export to Power BI",
                        "type": "n8n-nodes-base.httpRequest",
                        "parameters": {
                            "url": "http://localhost:8000/bi/export-powerbi",
                            "method": "POST",
                            "body": "={{ $json }}"
                        }
                    },
                    {
                        "name": "Export to Tableau",
                        "type": "n8n-nodes-base.httpRequest",
                        "parameters": {
                            "url": "http://localhost:8000/bi/export-tableau",
                            "method": "POST",
                            "body": "={{ $json }}"
                        }
                    }
                ]
            }

            # Créer le workflow via l'API n8n
            create_url = f"{self.config.endpoint.replace('/webhook', '')}/workflows"
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f"Bearer {self.config.credentials.get('api_key', '')}"
            }

            response = requests.post(create_url, json=workflow_data, headers=headers)

            if response.status_code in [200, 201]:
                workflow = response.json()
                return {
                    "success": True,
                    "message": "Workflow automatisé créé",
                    "workflow_id": workflow.get('id'),
                    "workflow_name": workflow_data["name"]
                }
            else:
                return {"success": False, "error": f"Erreur création workflow: {response.text}"}

        except Exception as e:
            return {"success": False, "error": str(e)}

class ERPNextConnector:
    """🟢 Connecteur ERPNext pour intégration temps réel"""

    def __init__(self, connector_config: BIConnector):
        self.config = connector_config
        self.session_id = None
        self.api_key = None
        self.api_secret = None

    async def authenticate(self) -> bool:
        """Authentification avec ERPNext"""
        try:
            # ERPNext utilise username/password ou API Key/Secret
            if 'api_key' in self.config.credentials and 'api_secret' in self.config.credentials:
                self.api_key = self.config.credentials['api_key']
                self.api_secret = self.config.credentials['api_secret']
                logger.info("Authentification ERPNext par API Key")
                return True
            else:
                # Authentification par login
                auth_url = f"{self.config.endpoint.replace('/api/resource/Project', '')}/api/method/login"

                data = {
                    'usr': self.config.credentials['username'],
                    'pwd': self.config.credentials['password']
                }

                response = requests.post(auth_url, data=data)
                if response.status_code == 200:
                    # ERPNext utilise les cookies de session
                    self.session_id = response.cookies
                    logger.info("Authentification ERPNext réussie")
                    return True
                else:
                    logger.warning(f"Authentification ERPNext échouée, mode démo activé")
                    return True  # Continue en mode démo

        except Exception as e:
            logger.error(f"Erreur authentification ERPNext: {e}")
            return True  # Continue en mode démo

    async def authenticate_sap(self) -> bool:
        """Authentification SAP spécifique"""
        try:
            auth_url = f"{self.config.endpoint}/sap/bc/rest/auth/login"

            auth_data = {
                "username": self.config.credentials['username'],
                "password": self.config.credentials['password'],
                "client": self.config.credentials.get('client', '100')
            }

            response = requests.post(auth_url, json=auth_data)
            if response.status_code == 200:
                self.session_id = response.json().get('session_id')
                logger.info("Authentification SAP réussie")
                return True
            else:
                logger.error(f"Erreur authentification SAP: {response.text}")
                return False

        except Exception as e:
            logger.error(f"Erreur authentification SAP: {e}")
            return False

    async def authenticate_generic(self) -> bool:
        """Authentification générique pour autres ERP"""
        try:
            auth_url = f"{self.config.endpoint}/auth"

            auth_data = {
                "username": self.config.credentials['username'],
                "password": self.config.credentials['password']
            }

            response = requests.post(auth_url, json=auth_data)
            if response.status_code == 200:
                self.session_id = response.json().get('token', response.json().get('session_id'))
                logger.info("Authentification ERP réussie")
                return True
            else:
                return False

        except Exception as e:
            return False

    async def sync_project_costs(self, bim_data: Dict[str, Any]) -> Dict[str, Any]:
        """Synchronise les coûts de projet avec l'ERP"""
        try:
            if not self.session_id:
                if not await self.authenticate():
                    return {"success": False, "error": "Authentification ERP échouée"}

            # Préparer les données de coûts pour l'ERP
            cost_data = {
                "project_id": bim_data["project_metadata"]["project_id"],
                "analysis_date": bim_data["project_metadata"]["analysis_date"],
                "cost_breakdown": bim_data.get("cost_metrics", {}),
                "quantities": {
                    "total_elements": bim_data["performance_kpis"]["total_elements"],
                    "floor_area_m2": bim_data["performance_kpis"].get("floor_area_m2", 0),
                    "volume_m3": bim_data["performance_kpis"].get("volume_m3", 0)
                },
                "materials": bim_data.get("material_breakdown", {})
            }

            # Envoyer vers l'ERP
            sync_url = f"{self.config.endpoint}/projects/costs"
            headers = {
                'Authorization': f'Bearer {self.session_id}',
                'Content-Type': 'application/json'
            }

            response = requests.post(sync_url, json=cost_data, headers=headers)

            if response.status_code in [200, 201]:
                return {
                    "success": True,
                    "message": "Coûts synchronisés avec l'ERP",
                    "erp_project_id": response.json().get('project_id')
                }
            else:
                return {"success": False, "error": f"Erreur sync ERP: {response.text}"}

        except Exception as e:
            return {"success": False, "error": str(e)}

# Instance globale du gestionnaire BI
bi_manager = BusinessIntelligenceManager()
