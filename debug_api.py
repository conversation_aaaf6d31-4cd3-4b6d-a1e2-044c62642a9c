#!/usr/bin/env python3
"""
🔍 Script de debug pour vérifier le format des données API
"""

import requests
import json
from pprint import pprint

def debug_api_responses():
    """Debug les réponses des APIs principales"""
    
    base_url = "http://localhost:8010"
    
    print("🔍 Debug des réponses API BIMEX")
    print("=" * 60)
    
    # Test 1: /projects
    print("\n📋 Endpoint: /projects")
    print("-" * 30)
    try:
        response = requests.get(f"{base_url}/projects", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {response.status_code}")
            print(f"📊 Type: {type(data)}")
            print(f"📏 Taille: {len(data) if hasattr(data, '__len__') else 'N/A'}")
            
            if isinstance(data, dict):
                print(f"🔑 Clés: {list(data.keys())}")
                if data:
                    first_key = list(data.keys())[0]
                    print(f"📄 Premier élément ({first_key}):")
                    pprint(data[first_key], indent=2, width=80)
            elif isinstance(data, list):
                print(f"📄 Premier élément:")
                if data:
                    pprint(data[0], indent=2, width=80)
            else:
                print(f"📄 Données:")
                pprint(data, indent=2, width=80)
        else:
            print(f"❌ Status: {response.status_code}")
            print(f"📄 Réponse: {response.text}")
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    # Test 2: /bi/status
    print("\n📊 Endpoint: /bi/status")
    print("-" * 30)
    try:
        response = requests.get(f"{base_url}/bi/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {response.status_code}")
            print(f"📊 Type: {type(data)}")
            print(f"📄 Structure:")
            pprint(data, indent=2, width=80)
        else:
            print(f"❌ Status: {response.status_code}")
            print(f"📄 Réponse: {response.text}")
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    # Test 3: Vérifier un projet spécifique
    print("\n🏗️ Test d'analyse de projet")
    print("-" * 30)
    try:
        # D'abord récupérer la liste des projets
        projects_response = requests.get(f"{base_url}/projects", timeout=5)
        if projects_response.status_code == 200:
            projects_data = projects_response.json()
            
            # Trouver un projet à tester
            project_id = None
            if isinstance(projects_data, dict) and projects_data:
                project_id = list(projects_data.keys())[0]
            elif isinstance(projects_data, list) and projects_data:
                project_id = projects_data[0].get('id') or projects_data[0].get('name')
            
            if project_id:
                print(f"🎯 Test avec projet: {project_id}")
                analysis_response = requests.get(f"{base_url}/analyze-comprehensive-project/{project_id}", timeout=10)
                print(f"📊 Status analyse: {analysis_response.status_code}")
                if analysis_response.status_code == 200:
                    analysis_data = analysis_response.json()
                    print(f"📄 Clés de l'analyse: {list(analysis_data.keys()) if isinstance(analysis_data, dict) else 'Non-dict'}")
                else:
                    print(f"📄 Erreur analyse: {analysis_response.text[:200]}...")
            else:
                print("❌ Aucun projet trouvé pour le test")
    except Exception as e:
        print(f"❌ Erreur test analyse: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Recommandations pour le dashboard:")
    print("1. Vérifiez le format des données dans la console du navigateur")
    print("2. Adaptez les fonctions JavaScript selon le format réel")
    print("3. Testez avec différents projets")
    print("=" * 60)

if __name__ == "__main__":
    debug_api_responses()
