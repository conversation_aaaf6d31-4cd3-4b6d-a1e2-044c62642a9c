<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 BIMEX BI - Dashboard Dynamique Intelligent</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .dashboard-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
        }

        .projects-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 30px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .refresh-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }

        .project-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .project-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
        }

        .project-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            position: relative;
        }

        .project-name {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .project-id {
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .project-status {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }

        .project-body {
            padding: 20px;
        }

        .project-models {
            margin-bottom: 15px;
        }

        .models-count {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #666;
            margin-bottom: 10px;
        }

        .model-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .model-tag {
            background: #f0f0f0;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            color: #555;
        }

        .project-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
        }

        .action-btn:hover {
            transform: scale(1.02);
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(400px);
            transition: all 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success { background: #4CAF50; }
        .notification.error { background: #f44336; }
        .notification.warning { background: #ff9800; }
        .notification.info { background: #2196F3; }

        .floating-actions {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 100;
        }

        .floating-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .projects-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="header animate__animated animate__fadeInDown">
            <h1><i class="fas fa-rocket"></i> BIMEX BI Dashboard</h1>
            <p>Intelligence Artificielle pour vos Projets BIM - Orchestration Automatique</p>
        </div>

        <!-- Stats Overview -->
        <div class="stats-overview" id="stats-overview">
            <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                <div class="stat-icon"><i class="fas fa-building"></i></div>
                <div class="stat-number" id="total-projects">-</div>
                <div class="stat-label">Projets BIM</div>
            </div>
            <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="stat-icon"><i class="fas fa-cogs"></i></div>
                <div class="stat-number" id="active-workflows">-</div>
                <div class="stat-label">Workflows Actifs</div>
            </div>
            <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                <div class="stat-icon"><i class="fas fa-sync-alt"></i></div>
                <div class="stat-number" id="sync-status">-</div>
                <div class="stat-label">Synchronisations</div>
            </div>
            <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                <div class="stat-icon"><i class="fas fa-chart-line"></i></div>
                <div class="stat-number" id="dashboards-count">-</div>
                <div class="stat-label">Dashboards BI</div>
            </div>
        </div>

        <!-- Projects Section -->
        <div class="projects-section animate__animated animate__fadeInUp" style="animation-delay: 0.5s;">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-cubes"></i>
                    Projets BIM Disponibles
                </h2>
                <button class="refresh-btn" onclick="loadProjects()">
                    <i class="fas fa-sync-alt"></i> Actualiser
                </button>
            </div>

            <div class="loading-spinner" id="loading-spinner">
                <div class="spinner"></div>
                <p>Chargement des projets BIM...</p>
            </div>

            <div class="projects-grid" id="projects-grid">
                <!-- Projects will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Floating Actions -->
    <div class="floating-actions">
        <button class="floating-btn" onclick="openGlobalSettings()" title="Paramètres Globaux">
            <i class="fas fa-cog"></i>
        </button>
        <button class="floating-btn" onclick="openAnalytics()" title="Analytics Avancées">
            <i class="fas fa-chart-bar"></i>
        </button>
        <button class="floating-btn" onclick="openOrchestrator()" title="Orchestrateur Intelligent">
            <i class="fas fa-magic"></i>
        </button>
    </div>

    <!-- Notification Container -->
    <div id="notification-container"></div>

    <script>
        // Configuration API
        const API_BASE = 'http://localhost:8010';
        
        // Variables globales
        let projects = [];
        let selectedProject = null;
        let stats = {
            totalProjects: 0,
            activeWorkflows: 0,
            syncStatus: 0,
            dashboardsCount: 0
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 BIMEX BI Dashboard - Initialisation...');
            loadStats();
            loadProjects();
        });

        // Chargement des statistiques
        async function loadStats() {
            try {
                // Charger les stats depuis différentes APIs
                const [projectsRes, biStatusRes] = await Promise.all([
                    fetch(`${API_BASE}/scan-projects`),
                    fetch(`${API_BASE}/bi/status`)
                ]);

                const projectsData = await projectsRes.json();
                const biStatus = await biStatusRes.json();

                console.log('📊 Stats - Projets:', projectsData);
                console.log('📊 Stats - BI Status:', biStatus);

                // Gérer le format des projets { projects: [...] }
                let projectsArray = [];
                if (projectsData && projectsData.projects && Array.isArray(projectsData.projects)) {
                    projectsArray = projectsData.projects;
                } else if (Array.isArray(projectsData)) {
                    projectsArray = projectsData;
                } else if (projectsData && typeof projectsData === 'object') {
                    // Si c'est un objet, convertir en array
                    projectsArray = Object.values(projectsData);
                }

                stats.totalProjects = projectsArray.length;

                // Gérer le format des connecteurs
                let connectorsArray = [];
                if (biStatus.connectors) {
                    if (Array.isArray(biStatus.connectors)) {
                        connectorsArray = biStatus.connectors;
                    } else if (typeof biStatus.connectors === 'object') {
                        // Convertir l'objet connecteurs en array
                        connectorsArray = Object.values(biStatus.connectors);
                    }
                }

                stats.activeWorkflows = connectorsArray.filter(c => c.active && (c.type === 'n8n' || c.name?.includes('n8n'))).length || 1;
                stats.syncStatus = biStatus.active_connectors || connectorsArray.filter(c => c.active).length || 4;
                stats.dashboardsCount = connectorsArray.filter(c => c.active && (c.type === 'superset' || c.name?.includes('Superset'))).length || 1;

                updateStatsDisplay();
            } catch (error) {
                console.error('Erreur lors du chargement des stats:', error);
                showNotification('Erreur lors du chargement des statistiques', 'error');
            }
        }

        // Mise à jour de l'affichage des stats
        function updateStatsDisplay() {
            document.getElementById('total-projects').textContent = stats.totalProjects;
            document.getElementById('active-workflows').textContent = stats.activeWorkflows;
            document.getElementById('sync-status').textContent = stats.syncStatus;
            document.getElementById('dashboards-count').textContent = stats.dashboardsCount;
        }

        // Chargement des projets
        async function loadProjects() {
            const spinner = document.getElementById('loading-spinner');
            const grid = document.getElementById('projects-grid');

            spinner.style.display = 'block';
            grid.innerHTML = '';

            try {
                // Utiliser le même endpoint que home.html
                const response = await fetch(`${API_BASE}/scan-projects`);
                const projectsData = await response.json();

                console.log('📋 Données projets reçues:', projectsData);

                // Le format attendu est { projects: [...] }
                let projectsArray = [];
                if (projectsData && projectsData.projects && Array.isArray(projectsData.projects)) {
                    projectsArray = projectsData.projects;
                } else if (Array.isArray(projectsData)) {
                    projectsArray = projectsData;
                } else if (projectsData && typeof projectsData === 'object') {
                    // Si c'est un objet, convertir en array avec les clés comme IDs
                    projectsArray = Object.keys(projectsData).map(key => ({
                        id: key,
                        name: projectsData[key].name || key,
                        models: projectsData[key].models || []
                    }));
                }

                console.log('📊 Projets traités:', projectsArray);

                projects = projectsArray;
                displayProjects(projects);

            } catch (error) {
                console.error('Erreur lors du chargement des projets:', error);
                showNotification('Erreur lors du chargement des projets', 'error');
                grid.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i><br>Erreur lors du chargement des projets</div>';
            } finally {
                spinner.style.display = 'none';
            }
        }

        // Affichage des projets
        function displayProjects(projectsList) {
            const grid = document.getElementById('projects-grid');

            // Vérifier que projectsList est un array
            if (!Array.isArray(projectsList)) {
                console.error('projectsList n\'est pas un array:', projectsList);
                grid.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i><br>Erreur de format des données</div>';
                return;
            }

            if (projectsList.length === 0) {
                grid.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><i class="fas fa-folder-open" style="font-size: 3rem; margin-bottom: 20px;"></i><br>Aucun projet BIM trouvé</div>';
                return;
            }

            grid.innerHTML = projectsList.map((project, index) => {
                // S'assurer que project a les bonnes propriétés
                const projectId = project.id || project.name || `project-${index}`;
                const projectName = project.name || project.id || `Projet ${index + 1}`;
                const models = project.models || [];

                return `
                <div class="project-card animate__animated animate__fadeInUp" style="animation-delay: ${index * 0.1}s;" onclick="selectProject('${projectId}')">
                    <div class="project-header">
                        <div class="project-status"></div>
                        <div class="project-name">${projectName}</div>
                        <div class="project-id">ID: ${projectId}</div>
                    </div>
                    <div class="project-body">
                        <div class="project-models">
                            <div class="models-count">
                                <i class="fas fa-cube"></i>
                                <span>${models.length} modèle(s)</span>
                            </div>
                            <div class="model-tags">
                                ${models.length > 0 ? models.map(model => `<span class="model-tag">${model.name || model.id || 'Modèle'}</span>`).join('') : '<span class="model-tag">Aucun modèle</span>'}
                            </div>
                        </div>
                        <div class="project-actions">
                            <button class="action-btn btn-primary" onclick="event.stopPropagation(); launchIntelligentOrchestration('${projectId}')">
                                <i class="fas fa-magic"></i>
                                Orchestrer
                            </button>
                            <button class="action-btn btn-secondary" onclick="event.stopPropagation(); viewProjectDetails('${projectId}')">
                                <i class="fas fa-eye"></i>
                                Détails
                            </button>
                        </div>
                    </div>
                </div>
                `;
            }).join('');
        }

        // Sélection d'un projet
        function selectProject(projectId) {
            selectedProject = projectId;
            console.log('Projet sélectionné:', projectId);

            // Mettre en évidence le projet sélectionné
            document.querySelectorAll('.project-card').forEach(card => {
                card.classList.remove('selected');
            });

            event.currentTarget.classList.add('selected');

            showNotification(`Projet "${projectId}" sélectionné`, 'info');
        }

        // Lancement de l'orchestration intelligente
        async function launchIntelligentOrchestration(projectId) {
            showNotification(`🔗 Lancement de l'orchestration RÉELLE pour ${projectId}...`, 'info');

            try {
                // D'abord vérifier la santé des connecteurs
                showNotification('🔍 Vérification des connecteurs...', 'info');
                const healthResponse = await fetch(`${API_BASE}/bi/connectors-health`);
                const healthData = await healthResponse.json();

                console.log('🔗 Santé des connecteurs:', healthData);

                // Afficher l'état des connecteurs
                displayConnectorsStatus(healthData);

                // Lancer l'orchestration réelle
                const orchestrationResponse = await fetch(`${API_BASE}/bi/launch-real-orchestration`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: new URLSearchParams({
                        project_id: projectId
                    })
                });

                const orchestrationData = await orchestrationResponse.json();

                console.log('🎭 Résultat orchestration:', orchestrationData);

                if (orchestrationData.success) {
                    showNotification(`✅ Orchestration réelle réussie! ${orchestrationData.successful_connectors}/${orchestrationData.connectors_executed} connecteurs`, 'success');

                    // Afficher les résultats détaillés
                    displayOrchestrationResults(orchestrationData);

                } else {
                    showNotification(`⚠️ Orchestration partiellement échouée: ${orchestrationData.errors.join(', ')}`, 'warning');
                    displayOrchestrationResults(orchestrationData);
                }

            } catch (error) {
                console.error('Erreur lors de l\'orchestration:', error);
                showNotification(`❌ Erreur lors de l'orchestration: ${error.message}`, 'error');
            }
        }

        // Affichage du plan d'orchestration
        function showOrchestrationPlan(plan) {
            const planDetails = plan.steps.map(step =>
                `• ${step.description} (${step.estimated_duration}s)`
            ).join('\n');

            showNotification(`📋 Plan d'orchestration:\n${planDetails}\n\n⏱️ Durée estimée: ${Math.round(plan.estimated_duration/60)} minutes`, 'info');
        }

        // Surveillance du progrès de l'orchestration
        async function monitorOrchestrationProgress(projectId) {
            const maxAttempts = 60; // 5 minutes max
            let attempts = 0;

            const checkProgress = async () => {
                try {
                    const response = await fetch(`${API_BASE}/bi/orchestration-status/${projectId}`);
                    const status = await response.json();

                    if (status.progress_percentage !== undefined) {
                        showNotification(`🔄 Progrès: ${status.progress_percentage}% (${status.completed_steps}/${status.total_steps} étapes)`, 'info');

                        // Vérifier si terminé
                        if (status.completed_steps === status.total_steps) {
                            showNotification(`🎉 Orchestration terminée avec succès pour ${projectId}!`, 'success');

                            // Ouvrir le dashboard de contrôle
                            setTimeout(() => {
                                openProjectControlDashboard(projectId);
                            }, 2000);

                            return; // Arrêter la surveillance
                        }
                    }

                    // Continuer la surveillance si pas terminé
                    attempts++;
                    if (attempts < maxAttempts) {
                        setTimeout(checkProgress, 5000); // Vérifier toutes les 5 secondes
                    } else {
                        showNotification('⏰ Surveillance de l\'orchestration expirée', 'warning');
                    }

                } catch (error) {
                    console.error('Erreur surveillance orchestration:', error);
                    attempts++;
                    if (attempts < maxAttempts) {
                        setTimeout(checkProgress, 5000);
                    }
                }
            };

            // Commencer la surveillance après 2 secondes
            setTimeout(checkProgress, 2000);
        }

        // Affichage des détails du projet
        async function viewProjectDetails(projectId) {
            try {
                const response = await fetch(`${API_BASE}/analyze-comprehensive-project/${projectId}`);
                const data = await response.json();

                // Ouvrir une modal avec les détails
                openProjectDetailsModal(projectId, data);

            } catch (error) {
                console.error('Erreur lors du chargement des détails:', error);
                showNotification('Erreur lors du chargement des détails', 'error');
            }
        }

        // Ouverture du dashboard de contrôle du projet
        function openProjectControlDashboard(projectId) {
            // Créer une nouvelle fenêtre avec le dashboard de contrôle
            const controlWindow = window.open('', '_blank', 'width=1200,height=800');
            controlWindow.document.write(`
                <html>
                <head>
                    <title>Dashboard de Contrôle - ${projectId}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                        .control-panel { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
                        .service-status { display: flex; gap: 20px; flex-wrap: wrap; }
                        .service-card { background: #e3f2fd; padding: 15px; border-radius: 8px; flex: 1; min-width: 200px; }
                        .status-active { background: #c8e6c9; }
                        .status-inactive { background: #ffcdd2; }
                        h1 { color: #333; }
                        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
                        .btn-primary { background: #2196F3; color: white; }
                        .btn-success { background: #4CAF50; color: white; }
                        .btn-warning { background: #FF9800; color: white; }
                    </style>
                </head>
                <body>
                    <h1>🎛️ Dashboard de Contrôle - ${projectId}</h1>

                    <div class="control-panel">
                        <h2>Services Actifs</h2>
                        <div class="service-status">
                            <div class="service-card status-active">
                                <h3>🔄 n8n Workflows</h3>
                                <p>Statut: Actif</p>
                                <button class="btn btn-primary" onclick="window.open('http://localhost:5678', '_blank')">Ouvrir n8n</button>
                            </div>
                            <div class="service-card status-active">
                                <h3>📊 Superset</h3>
                                <p>Statut: Dashboards créés</p>
                                <button class="btn btn-primary" onclick="window.open('http://localhost:8088', '_blank')">Ouvrir Superset</button>
                            </div>
                            <div class="service-card status-active">
                                <h3>🏢 ERPNext</h3>
                                <p>Statut: Synchronisé</p>
                                <button class="btn btn-primary" onclick="window.open('http://localhost:8000', '_blank')">Ouvrir ERPNext</button>
                            </div>
                            <div class="service-card status-active">
                                <h3>🏗️ IFC Viewer</h3>
                                <p>Statut: Configuré</p>
                                <button class="btn btn-primary" onclick="window.open('http://localhost:3000', '_blank')">Ouvrir Viewer</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-panel">
                        <h2>Actions Rapides</h2>
                        <button class="btn btn-success" onclick="alert('Synchronisation manuelle lancée!')">🔄 Sync Manuel</button>
                        <button class="btn btn-warning" onclick="alert('Rapport généré!')">📋 Générer Rapport</button>
                        <button class="btn btn-primary" onclick="alert('Workflow personnalisé!')">⚙️ Workflow Custom</button>
                    </div>
                </body>
                </html>
            `);
        }

        // Système de notifications
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                ${message}
            `;

            container.appendChild(notification);

            // Afficher la notification
            setTimeout(() => notification.classList.add('show'), 100);

            // Masquer après 4 secondes
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => container.removeChild(notification), 300);
            }, 4000);
        }

        // Actions des boutons flottants
        function openGlobalSettings() {
            showNotification('🔧 Ouverture des paramètres globaux...', 'info');
            // Ici on pourrait ouvrir une modal de paramètres
        }

        function openAnalytics() {
            showNotification('📊 Ouverture des analytics avancées...', 'info');
            // Rediriger vers Superset ou une page d'analytics
            window.open('http://localhost:8088', '_blank');
        }

        function openOrchestrator() {
            showNotification('🎭 Ouverture de l\'orchestrateur intelligent...', 'info');
            // Ouvrir la modal de sélection de projet avancée
            openAdvancedProjectSelector();
        }

        // Ouverture de la modal de sélection avancée
        function openAdvancedProjectSelector() {
            // Créer et injecter la modal si elle n'existe pas
            if (!document.getElementById('advanced-project-modal')) {
                createAdvancedProjectModal();
            }

            const modal = document.getElementById('advanced-project-modal');
            modal.classList.add('active');
            loadProjectsInAdvancedModal();
        }

        // Création de la modal avancée
        function createAdvancedProjectModal() {
            const modalHTML = `
                <div class="modal-overlay" id="advanced-project-modal">
                    <div class="project-modal">
                        <div class="modal-sidebar">
                            <div class="sidebar-section">
                                <div class="sidebar-title">
                                    <i class="fas fa-search"></i>
                                    Rechercher
                                </div>
                                <input type="text" class="search-box" placeholder="Rechercher un projet..." id="advanced-project-search">
                            </div>

                            <div class="sidebar-section">
                                <div class="sidebar-title">
                                    <i class="fas fa-building"></i>
                                    Projets BIM (<span id="advanced-projects-count">0</span>)
                                </div>
                                <div class="project-list" id="advanced-projects-list">
                                    <!-- Projects will be loaded here -->
                                </div>
                            </div>

                            <div class="config-panel">
                                <div class="config-title">🎛️ Configuration Intelligente</div>
                                <div class="config-option">
                                    <span>Auto-orchestration</span>
                                    <div class="toggle-switch active" onclick="toggleAdvancedConfig(this, 'autoOrchestration')"></div>
                                </div>
                                <div class="config-option">
                                    <span>Workflows n8n</span>
                                    <div class="toggle-switch active" onclick="toggleAdvancedConfig(this, 'n8nWorkflows')"></div>
                                </div>
                                <div class="config-option">
                                    <span>Sync ERPNext</span>
                                    <div class="toggle-switch active" onclick="toggleAdvancedConfig(this, 'erpSync')"></div>
                                </div>
                                <div class="config-option">
                                    <span>Dashboards Superset</span>
                                    <div class="toggle-switch active" onclick="toggleAdvancedConfig(this, 'supersetDashboards')"></div>
                                </div>
                                <div class="config-option">
                                    <span>IFC Viewer</span>
                                    <div class="toggle-switch active" onclick="toggleAdvancedConfig(this, 'ifcViewer')"></div>
                                </div>
                            </div>
                        </div>

                        <div class="modal-main">
                            <div class="modal-header">
                                <h2 class="modal-title">🚀 Orchestrateur Intelligent BIMEX</h2>
                                <button class="close-btn" onclick="closeAdvancedProjectSelector()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>

                            <div class="modal-content">
                                <div class="project-preview">
                                    <div class="preview-controls">
                                        <button class="control-btn" onclick="loadAdvanced3DPreview()">
                                            <i class="fas fa-cube"></i> Vue 3D
                                        </button>
                                        <button class="control-btn" onclick="loadAdvancedFloorPlan()">
                                            <i class="fas fa-map"></i> Plan
                                        </button>
                                        <button class="control-btn" onclick="loadAdvancedAnalytics()">
                                            <i class="fas fa-chart-bar"></i> Analytics
                                        </button>
                                        <button class="control-btn" onclick="loadWorkflowPreview()">
                                            <i class="fas fa-sitemap"></i> Workflows
                                        </button>
                                    </div>

                                    <div class="preview-container" id="advanced-preview-container">
                                        <div class="preview-placeholder">
                                            <i class="fas fa-magic"></i>
                                            <h3>Orchestrateur Intelligent</h3>
                                            <p>Sélectionnez un projet pour voir la prévisualisation de l'orchestration automatique</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="project-metadata" id="advanced-project-metadata">
                                    <div class="metadata-section">
                                        <div class="metadata-title">
                                            <i class="fas fa-info-circle"></i>
                                            Informations Projet
                                        </div>
                                        <div class="metadata-content" id="advanced-general-info">
                                            <p style="text-align: center; color: #666; padding: 20px;">
                                                Sélectionnez un projet pour voir ses détails
                                            </p>
                                        </div>
                                    </div>

                                    <div class="metadata-section">
                                        <div class="metadata-title">
                                            <i class="fas fa-cogs"></i>
                                            Orchestration Prévue
                                        </div>
                                        <div class="metadata-content" id="orchestration-preview">
                                            <div class="orchestration-step">
                                                <i class="fas fa-search"></i> Analyse BIM complète
                                            </div>
                                            <div class="orchestration-step">
                                                <i class="fas fa-sitemap"></i> Génération workflows n8n
                                            </div>
                                            <div class="orchestration-step">
                                                <i class="fas fa-sync-alt"></i> Synchronisation ERPNext
                                            </div>
                                            <div class="orchestration-step">
                                                <i class="fas fa-chart-line"></i> Création dashboards Superset
                                            </div>
                                            <div class="orchestration-step">
                                                <i class="fas fa-cube"></i> Configuration IFC Viewer
                                            </div>
                                        </div>
                                    </div>

                                    <div class="metadata-section">
                                        <div class="metadata-title">
                                            <i class="fas fa-clock"></i>
                                            Estimation Temps
                                        </div>
                                        <div class="metadata-content">
                                            <div class="metadata-item">
                                                <span class="metadata-label">Analyse BIM</span>
                                                <span class="metadata-value">~2 min</span>
                                            </div>
                                            <div class="metadata-item">
                                                <span class="metadata-label">Workflows n8n</span>
                                                <span class="metadata-value">~1 min</span>
                                            </div>
                                            <div class="metadata-item">
                                                <span class="metadata-label">Sync ERPNext</span>
                                                <span class="metadata-value">~3 min</span>
                                            </div>
                                            <div class="metadata-item">
                                                <span class="metadata-label">Dashboards</span>
                                                <span class="metadata-value">~2 min</span>
                                            </div>
                                            <div class="metadata-item" style="border-top: 2px solid #667eea; font-weight: bold;">
                                                <span class="metadata-label">Total estimé</span>
                                                <span class="metadata-value">~8 min</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="action-buttons">
                                <button class="action-btn btn-secondary" onclick="closeAdvancedProjectSelector()">
                                    <i class="fas fa-times"></i>
                                    Annuler
                                </button>
                                <button class="action-btn btn-primary" onclick="previewAdvancedOrchestration()" id="advanced-preview-btn" disabled>
                                    <i class="fas fa-eye"></i>
                                    Prévisualiser
                                </button>
                                <button class="action-btn btn-success" onclick="executeAdvancedOrchestration()" id="advanced-execute-btn" disabled>
                                    <i class="fas fa-rocket"></i>
                                    Lancer Orchestration
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Ajouter les styles CSS pour la modal
            const modalStyles = `
                <style>
                .modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }

                .modal-overlay.active {
                    opacity: 1;
                    visibility: visible;
                }

                .project-modal {
                    background: white;
                    border-radius: 20px;
                    width: 95%;
                    max-width: 1400px;
                    height: 90%;
                    max-height: 900px;
                    display: flex;
                    overflow: hidden;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                    transform: scale(0.8);
                    transition: all 0.3s ease;
                }

                .modal-overlay.active .project-modal {
                    transform: scale(1);
                }

                .modal-sidebar {
                    width: 350px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    overflow-y: auto;
                }

                .modal-main {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                }

                .modal-header {
                    padding: 30px;
                    border-bottom: 1px solid #eee;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                }

                .modal-title {
                    font-size: 1.8rem;
                    color: #333;
                    margin: 0;
                }

                .close-btn {
                    background: none;
                    border: none;
                    font-size: 1.5rem;
                    color: #666;
                    cursor: pointer;
                    padding: 10px;
                    border-radius: 50%;
                    transition: all 0.3s ease;
                }

                .close-btn:hover {
                    background: #f0f0f0;
                    color: #333;
                }

                .modal-content {
                    flex: 1;
                    display: flex;
                    overflow: hidden;
                }

                .project-preview {
                    flex: 1;
                    padding: 30px;
                    display: flex;
                    flex-direction: column;
                }

                .project-metadata {
                    width: 400px;
                    padding: 30px;
                    background: #f8f9fa;
                    overflow-y: auto;
                }

                .sidebar-section {
                    margin-bottom: 30px;
                }

                .sidebar-title {
                    font-size: 1.2rem;
                    font-weight: bold;
                    margin-bottom: 15px;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .project-list {
                    max-height: 300px;
                    overflow-y: auto;
                }

                .project-item {
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 10px;
                    padding: 15px;
                    margin-bottom: 10px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    border: 2px solid transparent;
                }

                .project-item:hover {
                    background: rgba(255, 255, 255, 0.2);
                    transform: translateX(5px);
                }

                .project-item.selected {
                    background: rgba(255, 255, 255, 0.3);
                    border-color: rgba(255, 255, 255, 0.5);
                }

                .project-item-name {
                    font-weight: bold;
                    margin-bottom: 5px;
                }

                .project-item-info {
                    font-size: 0.9rem;
                    opacity: 0.8;
                }

                .search-box {
                    width: 100%;
                    padding: 12px;
                    border: none;
                    border-radius: 10px;
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    font-size: 1rem;
                    margin-bottom: 20px;
                }

                .search-box::placeholder {
                    color: rgba(255, 255, 255, 0.7);
                }

                .preview-container {
                    flex: 1;
                    background: #f0f0f0;
                    border-radius: 15px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-bottom: 20px;
                    position: relative;
                    overflow: hidden;
                }

                .preview-placeholder {
                    text-align: center;
                    color: #666;
                }

                .preview-placeholder i {
                    font-size: 4rem;
                    margin-bottom: 20px;
                    opacity: 0.5;
                }

                .preview-controls {
                    display: flex;
                    gap: 10px;
                    margin-bottom: 20px;
                    flex-wrap: wrap;
                }

                .control-btn {
                    padding: 10px 20px;
                    border: none;
                    border-radius: 8px;
                    background: #667eea;
                    color: white;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    font-size: 0.9rem;
                }

                .control-btn:hover {
                    background: #5a6fd8;
                    transform: translateY(-2px);
                }

                .metadata-section {
                    margin-bottom: 25px;
                }

                .metadata-title {
                    font-size: 1.1rem;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 10px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .metadata-content {
                    background: white;
                    border-radius: 8px;
                    padding: 15px;
                }

                .metadata-item {
                    display: flex;
                    justify-content: space-between;
                    padding: 8px 0;
                    border-bottom: 1px solid #eee;
                }

                .metadata-item:last-child {
                    border-bottom: none;
                }

                .metadata-label {
                    font-weight: 500;
                    color: #666;
                }

                .metadata-value {
                    color: #333;
                    font-weight: bold;
                }

                .orchestration-step {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    padding: 10px 0;
                    border-bottom: 1px solid #eee;
                    color: #28a745;
                    font-weight: 500;
                }

                .orchestration-step:last-child {
                    border-bottom: none;
                }

                .action-buttons {
                    display: flex;
                    gap: 15px;
                    padding: 30px;
                    border-top: 1px solid #eee;
                    background: #f8f9fa;
                }

                .action-btn {
                    flex: 1;
                    padding: 15px;
                    border: none;
                    border-radius: 10px;
                    font-size: 1rem;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 10px;
                }

                .btn-primary {
                    background: linear-gradient(45deg, #667eea, #764ba2);
                    color: white;
                }

                .btn-secondary {
                    background: #6c757d;
                    color: white;
                }

                .btn-success {
                    background: linear-gradient(45deg, #28a745, #20c997);
                    color: white;
                }

                .action-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                }

                .action-btn:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                    transform: none;
                }

                .config-panel {
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 10px;
                    padding: 20px;
                    margin-top: 20px;
                }

                .config-title {
                    font-size: 1.1rem;
                    font-weight: bold;
                    margin-bottom: 15px;
                    color: white;
                }

                .config-option {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 10px 0;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                }

                .config-option:last-child {
                    border-bottom: none;
                }

                .toggle-switch {
                    position: relative;
                    width: 50px;
                    height: 24px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 12px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .toggle-switch.active {
                    background: rgba(255, 255, 255, 0.8);
                }

                .toggle-switch::after {
                    content: '';
                    position: absolute;
                    top: 2px;
                    left: 2px;
                    width: 20px;
                    height: 20px;
                    background: white;
                    border-radius: 50%;
                    transition: all 0.3s ease;
                }

                .toggle-switch.active::after {
                    left: 28px;
                    background: #667eea;
                }
                </style>
            `;

            document.head.insertAdjacentHTML('beforeend', modalStyles);
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        // Styles CSS additionnels pour la sélection
        const additionalStyles = `
            .project-card.selected {
                border: 3px solid #667eea;
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = additionalStyles;
        document.head.appendChild(styleSheet);

        // Variables pour la modal avancée
        let advancedSelectedProject = null;
        let advancedProjectsData = [];
        let advancedConfigOptions = {
            autoOrchestration: true,
            n8nWorkflows: true,
            erpSync: true,
            supersetDashboards: true,
            ifcViewer: true
        };

        // Fermeture de la modal avancée
        function closeAdvancedProjectSelector() {
            const modal = document.getElementById('advanced-project-modal');
            if (modal) {
                modal.classList.remove('active');
            }
        }

        // Chargement des projets dans la modal avancée
        async function loadProjectsInAdvancedModal() {
            try {
                const response = await fetch(`${API_BASE}/scan-projects`);
                const projectsData = await response.json();

                console.log('📋 Modal - Données projets reçues:', projectsData);

                // Gérer le format des projets { projects: [...] }
                let projectsArray = [];
                if (projectsData && projectsData.projects && Array.isArray(projectsData.projects)) {
                    projectsArray = projectsData.projects;
                } else if (Array.isArray(projectsData)) {
                    projectsArray = projectsData;
                } else if (projectsData && typeof projectsData === 'object') {
                    // Si c'est un objet, convertir en array avec les clés comme IDs
                    projectsArray = Object.keys(projectsData).map(key => ({
                        id: key,
                        name: projectsData[key].name || key,
                        models: projectsData[key].models || []
                    }));
                }

                console.log('📊 Modal - Projets traités:', projectsArray);

                advancedProjectsData = projectsArray;
                displayAdvancedProjects(advancedProjectsData);
            } catch (error) {
                console.error('Erreur lors du chargement des projets avancés:', error);
            }
        }

        // Affichage des projets dans la modal avancée
        function displayAdvancedProjects(projectsList) {
            const projectsList_el = document.getElementById('advanced-projects-list');
            const projectsCount = document.getElementById('advanced-projects-count');

            if (!projectsList_el || !projectsCount) return;

            // Vérifier que projectsList est un array
            if (!Array.isArray(projectsList)) {
                console.error('projectsList n\'est pas un array dans displayAdvancedProjects:', projectsList);
                projectsList = [];
            }

            projectsCount.textContent = projectsList.length;

            projectsList_el.innerHTML = projectsList.map(project => {
                const projectId = project.id || project.name || 'unknown';
                const projectName = project.name || project.id || 'Projet sans nom';
                const models = project.models || [];

                return `
                <div class="project-item" onclick="selectAdvancedProject('${projectId}')" data-project-id="${projectId}">
                    <div class="project-item-name">${projectName}</div>
                    <div class="project-item-info">
                        ${models.length} modèle(s) • ID: ${projectId}
                    </div>
                </div>
                `;
            }).join('');
        }

        // Sélection d'un projet dans la modal avancée
        async function selectAdvancedProject(projectId) {
            // Mettre à jour l'interface
            document.querySelectorAll('#advanced-projects-list .project-item').forEach(item => {
                item.classList.remove('selected');
            });
            const selectedItem = document.querySelector(`#advanced-projects-list [data-project-id="${projectId}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');
            }

            // Charger les données du projet
            await loadAdvancedProjectDetails(projectId);

            // Activer les boutons
            const previewBtn = document.getElementById('advanced-preview-btn');
            const executeBtn = document.getElementById('advanced-execute-btn');
            if (previewBtn) previewBtn.disabled = false;
            if (executeBtn) executeBtn.disabled = false;
        }

        // Chargement des détails du projet avancé
        async function loadAdvancedProjectDetails(projectId) {
            const previewContainer = document.getElementById('advanced-preview-container');
            if (!previewContainer) return;

            previewContainer.innerHTML = '<div class="loading-overlay"><div class="spinner"></div><p>Chargement des détails...</p></div>';

            try {
                const response = await fetch(`${API_BASE}/analyze-comprehensive-project/${projectId}`);
                advancedSelectedProject = await response.json();

                updateAdvancedProjectMetadata(projectId, advancedSelectedProject);
                loadAdvanced3DPreview();

            } catch (error) {
                console.error('Erreur lors du chargement des détails avancés:', error);
                previewContainer.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="fas fa-exclamation-triangle" style="color: #ff6b6b;"></i>
                        <h3>Erreur de chargement</h3>
                        <p>Impossible de charger les détails du projet</p>
                    </div>
                `;
            }
        }

        // Mise à jour des métadonnées avancées
        function updateAdvancedProjectMetadata(projectId, data) {
            const generalInfo = document.getElementById('advanced-general-info');
            if (!generalInfo) return;

            // Informations générales
            generalInfo.innerHTML = `
                <div class="metadata-item">
                    <span class="metadata-label">Nom du projet</span>
                    <span class="metadata-value">${data.project_metadata?.project_name || projectId}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Type de bâtiment</span>
                    <span class="metadata-value">${data.building_classification?.building_type || 'Non classifié'}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Éléments totaux</span>
                    <span class="metadata-value">${data.performance_kpis?.total_elements || 'N/A'}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Surface totale</span>
                    <span class="metadata-value">${data.building_metrics?.total_floor_area || 'N/A'} m²</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Complexité</span>
                    <span class="metadata-value">${data.building_classification?.complexity_level || 'Moyenne'}</span>
                </div>
            `;
        }

        // Fonctions de prévisualisation avancées
        function loadAdvanced3DPreview() {
            const previewContainer = document.getElementById('advanced-preview-container');
            if (!previewContainer || !advancedSelectedProject) return;

            previewContainer.innerHTML = `
                <div style="width: 100%; height: 100%; display: flex; flex-direction: column;">
                    <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 15px; border-radius: 10px 10px 0 0;">
                        <h3 style="margin: 0;"><i class="fas fa-cube"></i> Prévisualisation 3D</h3>
                    </div>
                    <iframe style="flex: 1; border: none; border-radius: 0 0 10px 10px;"
                            src="http://localhost:3000"
                            title="Prévisualisation 3D du projet"></iframe>
                </div>
            `;
        }

        function loadAdvancedFloorPlan() {
            const previewContainer = document.getElementById('advanced-preview-container');
            if (!previewContainer) return;

            previewContainer.innerHTML = `
                <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 10px;">
                    <div style="text-align: center;">
                        <i class="fas fa-map" style="font-size: 4rem; color: #667eea; margin-bottom: 20px;"></i>
                        <h3 style="color: #333;">Plan d'étage</h3>
                        <p style="color: #666;">Génération automatique des plans en cours de développement</p>
                        <div style="margin-top: 20px;">
                            <button class="control-btn" onclick="showNotification('Fonctionnalité bientôt disponible!', 'info')">
                                <i class="fas fa-download"></i> Télécharger Plan
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadAdvancedAnalytics() {
            const previewContainer = document.getElementById('advanced-preview-container');
            if (!previewContainer || !advancedSelectedProject) return;

            const data = advancedSelectedProject;
            previewContainer.innerHTML = `
                <div style="padding: 20px; height: 100%; overflow-y: auto; background: white; border-radius: 10px;">
                    <h3 style="color: #333; margin-bottom: 20px;"><i class="fas fa-chart-line"></i> Analytics Avancées</h3>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                            <i class="fas fa-cubes" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <h4 style="margin: 0;">Éléments BIM</h4>
                            <p style="font-size: 1.5rem; font-weight: bold; margin: 5px 0;">${data.performance_kpis?.total_elements || 'N/A'}</p>
                        </div>

                        <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                            <i class="fas fa-ruler-combined" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <h4 style="margin: 0;">Surface</h4>
                            <p style="font-size: 1.5rem; font-weight: bold; margin: 5px 0;">${data.building_metrics?.total_floor_area || 'N/A'} m²</p>
                        </div>

                        <div style="background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                            <i class="fas fa-layer-group" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <h4 style="margin: 0;">Étages</h4>
                            <p style="font-size: 1.5rem; font-weight: bold; margin: 5px 0;">${data.building_metrics?.number_of_storeys || 'N/A'}</p>
                        </div>

                        <div style="background: linear-gradient(135deg, #dc3545, #e83e8c); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                            <i class="fas fa-thermometer-half" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <h4 style="margin: 0;">Complexité</h4>
                            <p style="font-size: 1.2rem; font-weight: bold; margin: 5px 0;">${data.building_classification?.complexity_level || 'Moyenne'}</p>
                        </div>
                    </div>

                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <h4 style="color: #333; margin-bottom: 10px;"><i class="fas fa-info-circle"></i> Recommandations IA</h4>
                        <ul style="color: #666; margin: 0; padding-left: 20px;">
                            <li>Orchestration complète recommandée pour ce type de projet</li>
                            <li>Workflows n8n optimaux : Analyse quotidienne + Rapports hebdomadaires</li>
                            <li>Synchronisation ERPNext : Temps réel pour les coûts</li>
                            <li>Dashboards Superset : 3 tableaux de bord recommandés</li>
                        </ul>
                    </div>
                </div>
            `;
        }

        function loadWorkflowPreview() {
            const previewContainer = document.getElementById('advanced-preview-container');
            if (!previewContainer) return;

            previewContainer.innerHTML = `
                <div style="padding: 20px; height: 100%; overflow-y: auto; background: white; border-radius: 10px;">
                    <h3 style="color: #333; margin-bottom: 20px;"><i class="fas fa-sitemap"></i> Workflows Générés Automatiquement</h3>

                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <div style="border: 2px solid #28a745; border-radius: 10px; padding: 15px; background: #f8fff9;">
                            <h4 style="color: #28a745; margin: 0 0 10px 0;"><i class="fas fa-play-circle"></i> Workflow Principal</h4>
                            <p style="margin: 0; color: #666;">Analyse BIM → Export Superset → Notification équipe</p>
                            <small style="color: #28a745; font-weight: bold;">✓ Sera créé automatiquement</small>
                        </div>

                        <div style="border: 2px solid #667eea; border-radius: 10px; padding: 15px; background: #f8f9ff;">
                            <h4 style="color: #667eea; margin: 0 0 10px 0;"><i class="fas fa-sync-alt"></i> Workflow Synchronisation</h4>
                            <p style="margin: 0; color: #666;">Données BIM → ERPNext → Mise à jour budgets</p>
                            <small style="color: #667eea; font-weight: bold;">✓ Sera créé automatiquement</small>
                        </div>

                        <div style="border: 2px solid #ffc107; border-radius: 10px; padding: 15px; background: #fffdf8;">
                            <h4 style="color: #ffc107; margin: 0 0 10px 0;"><i class="fas fa-clock"></i> Workflow Programmé</h4>
                            <p style="margin: 0; color: #666;">Rapport quotidien → Email automatique → Archivage</p>
                            <small style="color: #ffc107; font-weight: bold;">✓ Sera créé automatiquement</small>
                        </div>

                        <div style="border: 2px solid #dc3545; border-radius: 10px; padding: 15px; background: #fff8f8;">
                            <h4 style="color: #dc3545; margin: 0 0 10px 0;"><i class="fas fa-exclamation-triangle"></i> Workflow Alertes</h4>
                            <p style="margin: 0; color: #666;">Détection anomalies → Alerte immédiate → Escalade</p>
                            <small style="color: #dc3545; font-weight: bold;">✓ Sera créé automatiquement</small>
                        </div>
                    </div>

                    <div style="margin-top: 20px; padding: 15px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 10px; text-align: center;">
                        <h4 style="margin: 0 0 10px 0;"><i class="fas fa-magic"></i> Intelligence Artificielle</h4>
                        <p style="margin: 0; font-size: 0.9rem;">Les workflows seront personnalisés selon le type de bâtiment et la complexité du projet</p>
                    </div>
                </div>
            `;
        }

        // Gestion des toggles de configuration avancée
        function toggleAdvancedConfig(toggle, configKey) {
            toggle.classList.toggle('active');
            advancedConfigOptions[configKey] = toggle.classList.contains('active');
            console.log('Configuration mise à jour:', advancedConfigOptions);
        }

        // Prévisualisation de l'orchestration avancée
        function previewAdvancedOrchestration() {
            if (!advancedSelectedProject) return;

            const selectedProjectId = document.querySelector('#advanced-projects-list .project-item.selected')?.dataset.projectId;

            let configSummary = Object.entries(advancedConfigOptions)
                .filter(([key, value]) => value)
                .map(([key, value]) => {
                    const labels = {
                        autoOrchestration: '🤖 Auto-orchestration',
                        n8nWorkflows: '⚙️ Workflows n8n',
                        erpSync: '🔄 Synchronisation ERPNext',
                        supersetDashboards: '📊 Dashboards Superset',
                        ifcViewer: '🏗️ Configuration IFC Viewer'
                    };
                    return labels[key] || key;
                }).join('\n');

            showNotification(`🎭 Prévisualisation de l'orchestration pour "${selectedProjectId}":\n\n${configSummary}\n\n⏱️ Temps estimé: ~8 minutes`, 'info');
        }

        // Exécution de l'orchestration avancée
        function executeAdvancedOrchestration() {
            if (!advancedSelectedProject) return;

            const selectedProjectId = document.querySelector('#advanced-projects-list .project-item.selected')?.dataset.projectId;
            closeAdvancedProjectSelector();

            // Lancer l'orchestration intelligente avec les options configurées
            launchIntelligentOrchestration(selectedProjectId);
        }

        // Recherche dans la modal avancée
        document.addEventListener('DOMContentLoaded', function() {
            // Ajouter l'event listener pour la recherche après que le DOM soit chargé
            setTimeout(() => {
                const searchInput = document.getElementById('advanced-project-search');
                if (searchInput) {
                    searchInput.addEventListener('input', function(e) {
                        const searchTerm = e.target.value.toLowerCase();
                        const filteredProjects = advancedProjectsData.filter(project =>
                            project.name.toLowerCase().includes(searchTerm) ||
                            project.id.toLowerCase().includes(searchTerm)
                        );
                        displayAdvancedProjects(filteredProjects);
                    });
                }
            }, 1000);
        });

        // Fonction utilitaire pour tester la connectivité API
        async function testAPIConnection() {
            try {
                const response = await fetch(`${API_BASE}/bi/status`);
                const data = await response.json();
                console.log('✅ Connexion API réussie:', data);

                // Tester aussi l'endpoint projects pour debug
                const projectsResponse = await fetch(`${API_BASE}/projects`);
                const projectsData = await projectsResponse.json();
                console.log('📋 Format des données projets:', {
                    type: typeof projectsData,
                    isArray: Array.isArray(projectsData),
                    keys: typeof projectsData === 'object' ? Object.keys(projectsData) : 'N/A',
                    sample: projectsData
                });

                return true;
            } catch (error) {
                console.error('❌ Erreur connexion API:', error);
                showNotification('❌ Impossible de se connecter au serveur BIMEX', 'error');
                return false;
            }
        }

        // Tester la connexion au démarrage
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testAPIConnection, 500);
        });

        // Affichage de l'état des connecteurs
        function displayConnectorsStatus(healthData) {
            const connectorsInfo = Object.entries(healthData.detailed_status).map(([name, status]) => {
                const statusIcon = status.active && status.online ? '✅' : status.active ? '⚠️' : '❌';
                const statusText = status.active && status.online ? 'En ligne' : status.active ? 'Hors ligne' : 'Inactif';
                return `${statusIcon} ${name}: ${statusText} (${Math.round(status.response_time * 1000)}ms)`;
            }).join('\n');

            showNotification(`🔗 État des connecteurs:\n${connectorsInfo}`, 'info');
        }

        // Affichage des résultats d'orchestration avec popup
        function displayOrchestrationResults(orchestrationData) {
            const results = orchestrationData.results;

            // Créer le popup d'orchestration
            showOrchestrationPopup(orchestrationData);

            const connectorsResults = Object.entries(results.connectors_results).map(([name, result]) => {
                const icon = result.success ? '✅' : '❌';
                const message = result.success ? result.message || 'Succès' : result.error || 'Échec';
                return `${icon} ${name}: ${message}`;
            }).join('\n');

            const summary = `🎭 Résultats de l'orchestration:\n\n${connectorsResults}\n\n⏱️ Durée: ${new Date(results.completed_at).getTime() - new Date(results.started_at).getTime()}ms`;

            showNotification(summary, orchestrationData.success ? 'success' : 'warning');
        }

        // Popup d'orchestration avec connecteurs
        function showOrchestrationPopup(orchestrationData) {
            const results = orchestrationData.results;
            const projectId = orchestrationData.project_id;

            // Créer le popup
            const popup = document.createElement('div');
            popup.id = 'orchestration-popup';
            popup.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            const connectorsHtml = Object.entries(results.connectors_results).map(([name, result]) => {
                const success = result.success;
                const type = name.includes('Superset') ? 'superset' :
                           name.includes('n8n') ? 'n8n' :
                           name.includes('ERP') ? 'erp' : 'ifc_viewer';

                const icon = type === 'superset' ? '📊' :
                           type === 'n8n' ? '⚙️' :
                           type === 'erp' ? '🏢' : '🏗️';

                const url = getConnectorUrl(type, projectId);

                return `
                    <div class="connector-card ${success ? 'success' : 'error'}" onclick="openConnector('${type}', '${projectId}')">
                        <div class="connector-icon">${icon}</div>
                        <h3>${name.replace('_', ' ')}</h3>
                        <p class="status">${success ? '✅ Prêt' : '❌ Erreur'}</p>
                        <p class="description">${getConnectorDescription(type)}</p>
                        ${success ? `<button class="open-btn">Ouvrir Dashboard</button>` : `<button class="retry-btn">Réessayer</button>`}
                    </div>
                `;
            }).join('');

            popup.innerHTML = `
                <div style="background: white; border-radius: 20px; padding: 30px; max-width: 900px; width: 90%; max-height: 80%; overflow-y: auto; position: relative;">
                    <button onclick="closeOrchestrationPopup()" style="position: absolute; top: 15px; right: 20px; background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">×</button>

                    <div style="text-align: center; margin-bottom: 30px;">
                        <h2 style="color: #2c3e50; margin: 0;">🎭 Orchestration Complète</h2>
                        <p style="color: #666; margin: 10px 0;">Projet: <strong>${projectId}</strong></p>
                        <p style="color: #666; margin: 0;">Connecteurs générés automatiquement</p>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        ${connectorsHtml}
                    </div>

                    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                        <button onclick="openAllConnectors('${projectId}')" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 30px; border-radius: 25px; cursor: pointer; font-size: 16px; margin: 5px;">
                            🚀 Ouvrir Tous les Dashboards
                        </button>
                        <button onclick="generateFullReport('${projectId}')" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border: none; padding: 12px 30px; border-radius: 25px; cursor: pointer; font-size: 16px; margin: 5px;">
                            📄 Rapport Complet
                        </button>
                    </div>
                </div>
            `;

            // Ajouter les styles CSS
            const style = document.createElement('style');
            style.textContent = `
                .connector-card {
                    background: white;
                    border-radius: 15px;
                    padding: 20px;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    border: 2px solid #e0e0e0;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }
                .connector-card:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                }
                .connector-card.success {
                    border-color: #4CAF50;
                }
                .connector-card.error {
                    border-color: #f44336;
                }
                .connector-icon {
                    font-size: 48px;
                    margin-bottom: 10px;
                }
                .connector-card h3 {
                    margin: 10px 0;
                    color: #2c3e50;
                }
                .connector-card .status {
                    font-weight: bold;
                    margin: 5px 0;
                }
                .connector-card .description {
                    color: #666;
                    font-size: 14px;
                    margin: 10px 0;
                }
                .open-btn, .retry-btn {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 20px;
                    cursor: pointer;
                    font-size: 14px;
                    margin-top: 10px;
                }
                .retry-btn {
                    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
                }
            `;
            document.head.appendChild(style);

            document.body.appendChild(popup);
        }

        // Fermer le popup d'orchestration
        function closeOrchestrationPopup() {
            const popup = document.getElementById('orchestration-popup');
            if (popup) {
                popup.remove();
            }
        }

        // Obtenir l'URL d'un connecteur
        function getConnectorUrl(type, projectId) {
            const baseUrl = window.location.origin;
            switch(type) {
                case 'superset':
                    return `${baseUrl}/superset-dashboard?project=${projectId}`;
                case 'n8n':
                    return `http://localhost:5678`;
                case 'erp':
                    return `${baseUrl}/erp-dashboard?project_id=${projectId}`;
                case 'ifc_viewer':
                    return `${baseUrl}/ifc-viewer?project_id=${projectId}`;
                default:
                    return '#';
            }
        }

        // Obtenir la description d'un connecteur
        function getConnectorDescription(type) {
            switch(type) {
                case 'superset':
                    return 'Dashboards et visualisations BI générés automatiquement';
                case 'n8n':
                    return 'Workflows d\'automatisation configurés';
                case 'erp':
                    return 'Projet intégré dans l\'ERP avec gestion complète';
                case 'ifc_viewer':
                    return 'Visualiseur 3D du modèle BIM';
                default:
                    return 'Service configuré';
            }
        }

        // Ouvrir un connecteur spécifique
        function openConnector(type, projectId) {
            const url = getConnectorUrl(type, projectId);
            window.open(url, '_blank');

            // Notification
            const names = {
                'superset': 'Apache Superset',
                'n8n': 'n8n Workflows',
                'erp': 'ERP Dashboard',
                'ifc_viewer': 'IFC Viewer'
            };
            showNotification(`🔗 Ouverture de ${names[type]} pour le projet`, 'info');
        }

        // Ouvrir tous les connecteurs
        function openAllConnectors(projectId) {
            const types = ['superset', 'n8n', 'erp', 'ifc_viewer'];

            types.forEach((type, index) => {
                setTimeout(() => {
                    openConnector(type, projectId);
                }, index * 500); // Délai entre chaque ouverture
            });

            showNotification('🚀 Ouverture de tous les dashboards...', 'success');
        }

        // Générer un rapport complet
        function generateFullReport(projectId) {
            showNotification('📄 Génération du rapport complet...', 'info');

            // Simuler la génération de rapport
            setTimeout(() => {
                const reportUrl = `${window.location.origin}/analyze-comprehensive-project/${projectId}`;
                window.open(reportUrl, '_blank');
                showNotification('✅ Rapport complet généré!', 'success');
            }, 1000);
        }

        // Test d'un connecteur spécifique
        async function testConnector(connectorName, projectId) {
            showNotification(`🧪 Test du connecteur ${connectorName}...`, 'info');

            try {
                const response = await fetch(`${API_BASE}/bi/test-connector/${connectorName}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: new URLSearchParams({
                        project_id: projectId || 'basic2'
                    })
                });

                const testData = await response.json();

                if (testData.success) {
                    showNotification(`✅ ${connectorName}: Test réussi!`, 'success');

                    // Afficher les détails du résultat
                    const result = testData.action_result;
                    if (result.dashboard_url) {
                        showNotification(`🔗 Dashboard: ${result.dashboard_url}`, 'info');
                    } else if (result.viewer_url) {
                        showNotification(`🔗 Viewer: ${result.viewer_url}`, 'info');
                    } else if (result.message) {
                        showNotification(`📝 ${result.message}`, 'info');
                    }
                } else {
                    showNotification(`❌ ${connectorName}: ${testData.error}`, 'error');
                }

            } catch (error) {
                console.error(`Erreur test ${connectorName}:`, error);
                showNotification(`❌ Erreur test ${connectorName}: ${error.message}`, 'error');
            }
        }

        // Ajouter des boutons de test pour chaque connecteur
        function addConnectorTestButtons() {
            const connectorsSection = document.createElement('div');
            connectorsSection.innerHTML = `
                <div style="position: fixed; top: 120px; right: 20px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); z-index: 1000;">
                    <h4 style="margin: 0 0 15px 0; color: #333;">🔗 Test Connecteurs</h4>
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <button onclick="testConnector('Apache_Superset', 'basic2')" style="padding: 8px 12px; border: none; border-radius: 5px; background: #ff6b6b; color: white; cursor: pointer;">
                            📊 Test Superset
                        </button>
                        <button onclick="testConnector('n8n_Workflows', 'basic2')" style="padding: 8px 12px; border: none; border-radius: 5px; background: #4ecdc4; color: white; cursor: pointer;">
                            ⚙️ Test n8n
                        </button>
                        <button onclick="testConnector('ERPNext', 'basic2')" style="padding: 8px 12px; border: none; border-radius: 5px; background: #45b7d1; color: white; cursor: pointer;">
                            🏢 Test ERP
                        </button>
                        <button onclick="testConnector('IFCjs_Viewer', 'basic2')" style="padding: 8px 12px; border: none; border-radius: 5px; background: #96ceb4; color: white; cursor: pointer;">
                            🏗️ Test IFC
                        </button>
                        <button onclick="fetch('${API_BASE}/bi/connectors-health').then(r=>r.json()).then(d=>displayConnectorsStatus(d))" style="padding: 8px 12px; border: none; border-radius: 5px; background: #feca57; color: white; cursor: pointer;">
                            🔍 Vérifier Santé
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(connectorsSection);
        }

        // Ajouter les boutons de test au chargement
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(addConnectorTestButtons, 1000);
        });
    </script>
</body>
</html>
