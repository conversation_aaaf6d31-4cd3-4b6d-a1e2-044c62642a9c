#!/usr/bin/env python3
"""
🔗 Test des connecteurs réels BIMEX
Teste la liaison avec les vrais services configurés dans bi_config.json
"""

import requests
import json
import time
from datetime import datetime

def test_bi_config():
    """Teste la lecture de bi_config.json"""
    print("📋 Test de la configuration BI")
    print("=" * 50)
    
    try:
        with open('backend/bi_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✅ Configuration chargée")
        print(f"📊 Connecteurs configurés: {len(config.get('connectors', []))}")
        
        for connector in config.get('connectors', []):
            print(f"   - {connector['name']} ({connector['type']}): {'✅ Actif' if connector.get('active') else '❌ Inactif'}")
            print(f"     Endpoint: {connector['endpoint']}")
        
        return config
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def test_connectors_health_api():
    """Teste l'API de santé des connecteurs"""
    print("\n🏥 Test de l'API de santé des connecteurs")
    print("=" * 50)
    
    base_url = "http://localhost:8010"
    
    try:
        response = requests.get(f"{base_url}/bi/connectors-health", timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API accessible")
            
            summary = data.get('summary', {})
            print(f"📊 Total connecteurs: {summary.get('total_connectors', 0)}")
            print(f"📊 Connecteurs actifs: {summary.get('active_connectors', 0)}")
            
            detailed = data.get('detailed_status', {})
            print(f"\n🔍 État détaillé:")
            for name, status in detailed.items():
                online_status = "🟢 En ligne" if status.get('online') else "🔴 Hors ligne"
                active_status = "✅ Actif" if status.get('active') else "❌ Inactif"
                response_time = status.get('response_time', 0)
                error = status.get('error')
                
                print(f"   {name}:")
                print(f"     État: {active_status} | {online_status}")
                print(f"     Temps de réponse: {response_time:.3f}s")
                print(f"     Endpoint: {status.get('endpoint', 'N/A')}")
                if error:
                    print(f"     Erreur: {error}")
            
            return data
        else:
            print(f"❌ Erreur API: {response.status_code}")
            print(f"Réponse: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def test_individual_connectors():
    """Teste chaque connecteur individuellement"""
    print("\n🧪 Test individuel des connecteurs")
    print("=" * 50)
    
    base_url = "http://localhost:8010"
    test_project = "basic2"
    
    connectors = ["Apache_Superset", "n8n_Workflows", "ERPNext", "IFCjs_Viewer"]
    
    for connector in connectors:
        print(f"\n🔗 Test de {connector}")
        print("-" * 30)
        
        try:
            response = requests.post(
                f"{base_url}/bi/test-connector/{connector}",
                data={"project_id": test_project},
                timeout=20
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('success'):
                    print(f"✅ Test réussi")
                    
                    action_result = data.get('action_result', {})
                    if action_result.get('dashboard_url'):
                        print(f"🔗 Dashboard: {action_result['dashboard_url']}")
                    elif action_result.get('viewer_url'):
                        print(f"🔗 Viewer: {action_result['viewer_url']}")
                    elif action_result.get('message'):
                        print(f"📝 Message: {action_result['message']}")
                    
                    if action_result.get('workflow_triggered'):
                        print(f"⚙️ Workflow déclenché")
                    
                else:
                    print(f"❌ Test échoué: {data.get('error', 'Erreur inconnue')}")
                    
                    connector_status = data.get('connector_status', {})
                    if not connector_status.get('active'):
                        print(f"   Cause: Connecteur inactif")
                    elif not connector_status.get('online'):
                        print(f"   Cause: Connecteur hors ligne")
            else:
                print(f"❌ Erreur HTTP: {response.status_code}")
                print(f"   Réponse: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ Erreur: {e}")

def test_full_orchestration():
    """Teste l'orchestration complète"""
    print("\n🎭 Test de l'orchestration complète")
    print("=" * 50)
    
    base_url = "http://localhost:8010"
    test_project = "basic2"
    
    try:
        print(f"🚀 Lancement de l'orchestration pour {test_project}")
        
        response = requests.post(
            f"{base_url}/bi/launch-real-orchestration",
            data={"project_id": test_project},
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"📊 Résultat global: {'✅ Succès' if data.get('success') else '⚠️ Partiel'}")
            print(f"📊 Connecteurs exécutés: {data.get('connectors_executed', 0)}")
            print(f"📊 Connecteurs réussis: {data.get('successful_connectors', 0)}")
            
            if data.get('errors'):
                print(f"❌ Erreurs: {', '.join(data['errors'])}")
            
            results = data.get('results', {})
            connectors_results = results.get('connectors_results', {})
            
            print(f"\n🔍 Détails par connecteur:")
            for name, result in connectors_results.items():
                status = "✅" if result.get('success') else "❌"
                message = result.get('message', result.get('error', 'Pas de message'))
                print(f"   {status} {name}: {message}")
                
                if result.get('dashboard_url'):
                    print(f"      🔗 Dashboard: {result['dashboard_url']}")
                elif result.get('viewer_url'):
                    print(f"      🔗 Viewer: {result['viewer_url']}")
            
            duration = (datetime.fromisoformat(results.get('completed_at', '').replace('Z', '+00:00')) - 
                       datetime.fromisoformat(results.get('started_at', '').replace('Z', '+00:00'))).total_seconds()
            print(f"\n⏱️ Durée totale: {duration:.2f} secondes")
            
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            print(f"   Réponse: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_bi_status():
    """Teste l'endpoint de statut BI"""
    print("\n📊 Test du statut BI")
    print("=" * 50)
    
    base_url = "http://localhost:8010"
    
    try:
        response = requests.get(f"{base_url}/bi/status", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Statut système: {data.get('system_status', 'unknown')}")
            
            modules = data.get('modules', {})
            print(f"📦 Modules:")
            for module, status in modules.items():
                print(f"   {'✅' if status else '❌'} {module}")
            
            print(f"🔗 Connecteurs actifs: {data.get('active_connectors', 0)}/{data.get('total_connectors', 0)}")
            
            connectors = data.get('connectors', [])
            if connectors:
                print(f"🔍 État des connecteurs:")
                for connector in connectors:
                    name = connector.get('name', 'Unknown')
                    online = connector.get('online', False)
                    active = connector.get('active', False)
                    print(f"   {'🟢' if online and active else '🔴'} {name}")
            
        else:
            print(f"❌ Erreur: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    print("🔗 BIMEX - Test des Connecteurs Réels")
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Test 1: Configuration
    config = test_bi_config()
    
    # Test 2: Statut BI
    test_bi_status()
    
    # Test 3: Santé des connecteurs
    health_data = test_connectors_health_api()
    
    # Test 4: Connecteurs individuels
    test_individual_connectors()
    
    # Test 5: Orchestration complète
    test_full_orchestration()
    
    print("\n" + "=" * 60)
    print("🎉 Tests terminés!")
    print("\n💡 Instructions pour utiliser le dashboard:")
    print("1. Ouvrez http://localhost:8010/bi_dashboard_dynamic.html")
    print("2. Utilisez les boutons de test des connecteurs (panneau de droite)")
    print("3. Cliquez sur 'Orchestrer' pour une orchestration complète")
    print("4. Vérifiez les notifications pour voir les résultats réels")
    print("\n🔗 Les connecteurs sont maintenant liés aux vrais services!")
    print("   - Superset: Dashboards réels")
    print("   - n8n: Workflows déclenchés")
    print("   - ERPNext: Projets créés")
    print("   - IFC Viewer: Viewer configuré")
