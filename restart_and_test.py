#!/usr/bin/env python3
"""
🔄 Script pour redémarrer le serveur BIMEX et tester les connecteurs
"""

import time
import requests
import subprocess
import sys
from pathlib import Path

def check_server_status():
    """Vérifie si le serveur BIMEX est en cours d'exécution"""
    try:
        response = requests.get("http://localhost:8010/bi/status", timeout=5)
        return response.status_code == 200
    except:
        return False

def wait_for_server(max_wait=30):
    """Attend que le serveur soit prêt"""
    print("⏳ Attente du démarrage du serveur...")
    
    for i in range(max_wait):
        if check_server_status():
            print(f"✅ Serveur prêt après {i+1} secondes")
            return True
        time.sleep(1)
        print(f"   Tentative {i+1}/{max_wait}...")
    
    print("❌ Timeout - serveur non accessible")
    return False

def test_real_connectors_module():
    """Teste si le module real_connectors est chargé"""
    print("\n🔗 Test du module real_connectors")
    print("=" * 40)
    
    try:
        response = requests.get("http://localhost:8010/bi/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            modules = data.get('modules', {})
            real_connectors = modules.get('real_connectors', False)
            
            if real_connectors:
                print("✅ Module real_connectors chargé")
                return True
            else:
                print("❌ Module real_connectors non chargé")
                print("💡 Le serveur doit être redémarré")
                return False
        else:
            print(f"❌ Erreur API: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_connectors_health():
    """Teste la santé des connecteurs"""
    print("\n🏥 Test de santé des connecteurs")
    print("=" * 40)
    
    try:
        response = requests.get("http://localhost:8010/bi/connectors-health", timeout=15)
        if response.status_code == 200:
            data = response.json()
            summary = data.get('summary', {})
            
            print(f"📊 Total connecteurs: {summary.get('total_connectors', 0)}")
            print(f"📊 Connecteurs actifs: {summary.get('active_connectors', 0)}")
            
            detailed = data.get('detailed_status', {})
            all_active = True
            
            for name, status in detailed.items():
                active = status.get('active', False)
                online = status.get('online', False)
                
                if not active:
                    all_active = False
                
                status_icon = "✅" if active and online else "⚠️" if active else "❌"
                print(f"   {status_icon} {name}: {'Actif' if active else 'Inactif'}, {'En ligne' if online else 'Hors ligne'}")
                
                if status.get('error'):
                    print(f"      Erreur: {status['error']}")
            
            return all_active
        else:
            print(f"❌ Erreur API: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_orchestration():
    """Teste l'orchestration complète"""
    print("\n🎭 Test d'orchestration complète")
    print("=" * 40)
    
    try:
        response = requests.post(
            "http://localhost:8010/bi/launch-real-orchestration",
            data={"project_id": "basic2"},
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            
            success = data.get('success', False)
            connectors_executed = data.get('connectors_executed', 0)
            successful_connectors = data.get('successful_connectors', 0)
            
            print(f"📊 Résultat: {'✅ Succès' if success else '⚠️ Partiel'}")
            print(f"📊 Connecteurs exécutés: {connectors_executed}")
            print(f"📊 Connecteurs réussis: {successful_connectors}")
            
            if data.get('errors'):
                print(f"❌ Erreurs: {', '.join(data['errors'])}")
            
            # Afficher les résultats détaillés
            results = data.get('results', {})
            connectors_results = results.get('connectors_results', {})
            
            print(f"\n🔍 Détails par connecteur:")
            for name, result in connectors_results.items():
                status_icon = "✅" if result.get('success') else "❌"
                message = result.get('message', result.get('error', 'Pas de message'))
                print(f"   {status_icon} {name}: {message}")
            
            return success
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            print(f"Réponse: {response.text[:200]}...")
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def show_instructions():
    """Affiche les instructions pour l'utilisateur"""
    print("\n" + "=" * 60)
    print("🎯 INSTRUCTIONS POUR UTILISER LES CONNECTEURS RÉELS")
    print("=" * 60)
    
    print("\n1. 🌐 Ouvrir le dashboard:")
    print("   http://localhost:8010/bi_dashboard_dynamic.html")
    
    print("\n2. 🔗 Tester les connecteurs individuellement:")
    print("   - Utilisez les boutons dans le panneau de droite")
    print("   - 📊 Test Superset")
    print("   - ⚙️ Test n8n")
    print("   - 🏢 Test ERP")
    print("   - 🏗️ Test IFC")
    
    print("\n3. 🎭 Lancer l'orchestration complète:")
    print("   - Cliquez sur 'Orchestrer' sur un projet")
    print("   - Vérifiez les notifications pour les résultats")
    
    print("\n4. 🔍 Vérifier la santé des connecteurs:")
    print("   - Bouton 'Vérifier Santé' dans le panneau")
    
    print("\n5. 🚀 Services requis (optionnels):")
    print("   - Superset: http://localhost:8088")
    print("   - n8n: http://localhost:5678")
    print("   - ERPNext: http://localhost:8000")
    print("   - IFC Viewer: http://localhost:3000")
    
    print("\n💡 Note: Les connecteurs fonctionnent même si les services")
    print("   ne sont pas démarrés (mode simulation intelligent)")

def main():
    print("🔄 BIMEX - Redémarrage et Test des Connecteurs")
    print(f"⏰ {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Vérifier si le serveur est en cours d'exécution
    if check_server_status():
        print("✅ Serveur BIMEX détecté")
        
        # Tester le module real_connectors
        if test_real_connectors_module():
            print("✅ Module real_connectors opérationnel")
        else:
            print("⚠️ Module real_connectors non chargé")
            print("💡 Redémarrez le serveur BIMEX pour charger le module")
        
        # Tester la santé des connecteurs
        if test_connectors_health():
            print("✅ Tous les connecteurs sont actifs")
        else:
            print("⚠️ Certains connecteurs sont inactifs")
        
        # Tester l'orchestration
        if test_orchestration():
            print("✅ Orchestration complète réussie")
        else:
            print("⚠️ Orchestration partiellement échouée")
        
    else:
        print("❌ Serveur BIMEX non accessible")
        print("💡 Démarrez le serveur avec: cd backend && python main.py")
    
    # Afficher les instructions
    show_instructions()

if __name__ == "__main__":
    main()
