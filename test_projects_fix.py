#!/usr/bin/env python3
"""
🧪 Test des corrections pour l'affichage des projets
"""

import requests
import json
from pathlib import Path

def test_projects_directory():
    """Teste si le répertoire des projets est accessible"""
    print("🔍 Test du répertoire des projets")
    print("=" * 40)
    
    possible_paths = [
        Path("xeokit-bim-viewer/app/data/projects"),
        Path("../xeokit-bim-viewer/app/data/projects"),
        Path("./xeokit-bim-viewer/app/data/projects"),
        Path("app/data/projects")
    ]
    
    for i, path in enumerate(possible_paths):
        exists = path.exists()
        print(f"{i+1}. {path}: {'✅ Existe' if exists else '❌ N\'existe pas'}")
        
        if exists:
            # Lister les projets
            projects = [p for p in path.iterdir() if p.is_dir() and p.name not in ["__pycache__", ".git"]]
            print(f"   📁 Projets trouvés: {len(projects)}")
            for project in projects[:5]:  # Limiter à 5
                print(f"     - {project.name}")
                
                # Vérifier les modèles
                models_dir = project / "models"
                if models_dir.exists():
                    model_dirs = [m for m in models_dir.iterdir() if m.is_dir()]
                    print(f"       📦 Modèles: {len(model_dirs)}")
                    for model_dir in model_dirs:
                        ifc_files = list(model_dir.glob("*.ifc"))
                        if ifc_files:
                            print(f"         🏗️ {model_dir.name}: {ifc_files[0].name}")
            break

def test_api_endpoints():
    """Teste les endpoints API"""
    print("\n🌐 Test des endpoints API")
    print("=" * 40)
    
    base_url = "http://localhost:8010"
    
    # Test 1: /scan-projects
    print("\n1. Test /scan-projects")
    try:
        response = requests.get(f"{base_url}/scan-projects", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {response.status_code}")
            
            if isinstance(data, dict) and "projects" in data:
                projects = data["projects"]
                print(f"📋 Projets trouvés: {len(projects)}")
                
                for project in projects[:3]:
                    print(f"   - {project.get('name', 'Sans nom')}: {project.get('models_count', 0)} modèle(s)")
                    if project.get('has_ifc_files'):
                        print(f"     ✅ Fichiers IFC détectés")
            else:
                print(f"❌ Format inattendu: {type(data)}")
        else:
            print(f"❌ Status: {response.status_code}")
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    # Test 2: /projects
    print("\n2. Test /projects")
    try:
        response = requests.get(f"{base_url}/projects", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {response.status_code}")
            print(f"📊 Type: {type(data)}")
            
            if isinstance(data, dict) and "projects" in data:
                print(f"📋 Projets: {len(data['projects'])}")
            elif isinstance(data, list):
                print(f"📋 Projets: {len(data)}")
        else:
            print(f"❌ Status: {response.status_code}")
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    # Test 3: /bi/status
    print("\n3. Test /bi/status")
    try:
        response = requests.get(f"{base_url}/bi/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {response.status_code}")
            print(f"📊 Modules actifs: {sum(data.get('modules', {}).values())}")
        else:
            print(f"❌ Status: {response.status_code}")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_orchestration_with_real_project():
    """Teste l'orchestration avec un vrai projet"""
    print("\n🎭 Test d'orchestration")
    print("=" * 40)
    
    base_url = "http://localhost:8010"
    
    # D'abord récupérer la liste des projets
    try:
        response = requests.get(f"{base_url}/scan-projects", timeout=10)
        if response.status_code == 200:
            data = response.json()
            
            if data and "projects" in data and data["projects"]:
                # Prendre le premier projet avec des modèles
                test_project = None
                for project in data["projects"]:
                    if project.get("models_count", 0) > 0:
                        test_project = project
                        break
                
                if test_project:
                    project_id = test_project["id"]
                    print(f"🎯 Test avec projet: {project_id}")
                    
                    # Tester l'orchestration (sans l'exécuter vraiment)
                    print("   (Test d'orchestration désactivé pour éviter les erreurs)")
                    # orchestration_data = {
                    #     'project_id': project_id,
                    #     'auto_orchestration': 'true',
                    #     'n8n_workflows': 'true',
                    #     'erp_sync': 'false',  # Désactiver pour le test
                    #     'superset_dashboards': 'false',
                    #     'ifc_viewer': 'true'
                    # }
                    # 
                    # response = requests.post(f"{base_url}/bi/launch-intelligent-orchestration", 
                    #                         data=orchestration_data, timeout=30)
                    # print(f"   Status orchestration: {response.status_code}")
                else:
                    print("❌ Aucun projet avec modèles trouvé")
            else:
                print("❌ Aucun projet disponible")
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    print("🚀 Test des corrections pour l'affichage des projets")
    print(f"⏰ {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_projects_directory()
    test_api_endpoints()
    test_orchestration_with_real_project()
    
    print("\n🎉 Tests terminés!")
    print("\n💡 Instructions:")
    print("1. Assurez-vous que le serveur BIMEX tourne sur le port 8010")
    print("2. Ouvrez http://localhost:8010/bi_dashboard_dynamic.html")
    print("3. Les projets devraient maintenant s'afficher avec leurs modèles")
    print("4. Testez l'orchestration sur un projet avec des fichiers IFC")
