#!/usr/bin/env python3
"""
🚀 Test des dashboards optimisés BIMEX
"""

import requests
import time

def test_optimized_dashboards():
    """Teste les dashboards optimisés"""
    
    print("🚀 Test des Dashboards Optimisés BIMEX")
    print("=" * 50)
    
    base_url = "http://localhost:8010"
    test_project = "basic2"
    
    # Test 1: Serveur
    print("1. 🔍 Test serveur...")
    try:
        response = requests.get(f"{base_url}/bi/status", timeout=5)
        if response.status_code == 200:
            print("   ✅ Serveur accessible")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ Serveur non accessible: {e}")
        return
    
    # Test 2: Dashboards optimisés
    print("\n2. 📊 Test dashboards optimisés...")
    
    dashboards = [
        ("ERP Dashboard", f"{base_url}/erp-dashboard?project_id={test_project}"),
        ("Superset Dashboard", f"{base_url}/superset-dashboard?project={test_project}"),
        ("IFC Viewer", f"{base_url}/ifc-viewer?project_id={test_project}")
    ]
    
    working_dashboards = []
    
    for name, url in dashboards:
        print(f"\n   🔗 Test {name}...")
        try:
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                content_size = len(response.content)
                print(f"     ✅ Accessible")
                print(f"     📄 Taille: {content_size} bytes")
                
                # Vérifier le contenu
                content = response.text.lower()
                
                if name == "ERP Dashboard":
                    if "en cours" in content and "budget" in content:
                        print(f"     ✅ Contenu ERP valide")
                        working_dashboards.append(name)
                    else:
                        print(f"     ⚠️ Contenu ERP incomplet")
                        
                elif name == "Superset Dashboard":
                    if "éléments bim" in content and "avancement" in content:
                        print(f"     ✅ Contenu Superset valide")
                        working_dashboards.append(name)
                    else:
                        print(f"     ⚠️ Contenu Superset incomplet")
                        
                elif name == "IFC Viewer":
                    if "visualiseur" in content or "viewer" in content:
                        print(f"     ✅ Contenu IFC valide")
                        working_dashboards.append(name)
                    else:
                        print(f"     ⚠️ Contenu IFC incomplet")
                        
            else:
                print(f"     ❌ Erreur {response.status_code}")
                
        except Exception as e:
            print(f"     ❌ Erreur: {e}")
    
    # Test 3: Orchestration
    print(f"\n3. 🎭 Test orchestration...")
    try:
        response = requests.post(
            f"{base_url}/bi/launch-real-orchestration",
            data={"project_id": test_project},
            timeout=20
        )
        
        if response.status_code == 200:
            data = response.json()
            successful = data.get('successful_connectors', 0)
            total = data.get('connectors_executed', 0)
            print(f"   ✅ Orchestration réussie: {successful}/{total}")
        else:
            print(f"   ❌ Erreur orchestration: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Erreur orchestration: {e}")
    
    # Résultats
    print(f"\n📊 RÉSULTATS:")
    print(f"   ✅ Dashboards fonctionnels: {len(working_dashboards)}/3")
    
    for dashboard in working_dashboards:
        print(f"   ✅ {dashboard}")
    
    if len(working_dashboards) >= 2:
        print(f"\n🎉 SYSTÈME OPÉRATIONNEL!")
        print(f"🎯 Instructions:")
        print(f"1. Ouvrez: http://localhost:8010/bi_dashboard_dynamic.html")
        print(f"2. Cliquez sur 'Orchestrer' sur {test_project}")
        print(f"3. Le popup s'ouvrira avec les connecteurs")
        print(f"4. Testez les URLs:")
        for name, url in dashboards:
            if name.replace(" Dashboard", "").replace(" ", "_").lower() in [d.replace(" Dashboard", "").replace(" ", "_").lower() for d in working_dashboards]:
                print(f"   ✅ {name}: {url}")
    else:
        print(f"\n⚠️ Quelques dashboards ont encore des problèmes")
    
    return len(working_dashboards)

if __name__ == "__main__":
    working_count = test_optimized_dashboards()
    
    if working_count >= 2:
        print(f"\n🚀 PRÊT POUR LA DÉMONSTRATION!")
        print(f"Les dashboards optimisés résolvent l'erreur Content-Length")
    else:
        print(f"\n🔧 Corrections supplémentaires nécessaires")
