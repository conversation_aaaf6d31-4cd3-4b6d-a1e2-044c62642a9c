#!/usr/bin/env python3
"""
🎭 Test du popup d'orchestration BIMEX
"""

import requests
import time
from datetime import datetime

def test_orchestration_popup():
    """Teste l'orchestration avec popup"""
    
    print("🎭 Test du Popup d'Orchestration BIMEX")
    print("=" * 50)
    
    base_url = "http://localhost:8010"
    test_project = "basic2"
    
    print(f"🎯 Projet de test: {test_project}")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S')}")
    
    # Test 1: Lancer l'orchestration
    print("\n1. 🎭 Lancement de l'orchestration...")
    try:
        response = requests.post(
            f"{base_url}/bi/launch-real-orchestration",
            data={"project_id": test_project},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            connectors_executed = data.get('connectors_executed', 0)
            successful_connectors = data.get('successful_connectors', 0)
            
            print(f"   ✅ Orchestration lancée")
            print(f"   📊 Connecteurs exécutés: {connectors_executed}")
            print(f"   📊 Connecteurs réussis: {successful_connectors}")
            
            # Afficher les résultats par connecteur
            results = data.get('results', {})
            connectors_results = results.get('connectors_results', {})
            
            print(f"\n   🔍 Résultats par connecteur:")
            for name, result in connectors_results.items():
                status = "✅" if result.get('success') else "❌"
                message = result.get('message', result.get('error', 'Pas de message'))
                print(f"     {status} {name}: {message}")
            
            return connectors_results
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            return {}
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return {}

def test_individual_dashboards(connectors_results):
    """Teste l'accès aux dashboards individuels"""
    
    print("\n2. 📊 Test des dashboards individuels...")
    
    base_url = "http://localhost:8010"
    test_project = "basic2"
    
    # URLs à tester
    dashboards = [
        ("Apache Superset", f"{base_url}/superset-dashboard?project={test_project}"),
        ("IFC Viewer", f"{base_url}/ifc-viewer?project_id={test_project}"),
        ("ERP Dashboard", f"{base_url}/erp-dashboard?project_id={test_project}"),
        ("n8n Workflows", "http://localhost:5678")
    ]
    
    working_dashboards = []
    
    for name, url in dashboards:
        print(f"\n   🔗 Test {name}...")
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"     ✅ Accessible")
                print(f"     📄 Taille: {len(response.content)} bytes")
                working_dashboards.append((name, url))
            else:
                print(f"     ❌ Erreur {response.status_code}")
        except Exception as e:
            print(f"     ❌ Erreur: {e}")
    
    return working_dashboards

def show_popup_simulation(connectors_results, working_dashboards):
    """Simule l'affichage du popup d'orchestration"""
    
    print("\n3. 🎭 Simulation du Popup d'Orchestration")
    print("=" * 50)
    
    print("""
┌─────────────────────────────────────────────────────────────┐
│                🎭 Orchestration Complète                    │
│                   Projet: basic2                           │
│             Connecteurs générés automatiquement            │
├─────────────────────────────────────────────────────────────┤""")
    
    # Afficher les connecteurs en grille 2x2
    connectors = [
        ("📊 Apache Superset", "Apache_Superset"),
        ("🏗️ IFC Viewer", "IFCjs_Viewer"),
        ("⚙️ n8n Workflows", "n8n_Workflows"),
        ("🏢 ERP Dashboard", "ERPNext")
    ]
    
    for i in range(0, len(connectors), 2):
        left = connectors[i]
        right = connectors[i+1] if i+1 < len(connectors) else ("", "")
        
        left_status = "✅ Prêt" if connectors_results.get(left[1], {}).get('success') else "❌ Erreur"
        right_status = "✅ Prêt" if connectors_results.get(right[1], {}).get('success') else "❌ Erreur"
        
        print(f"│  {left[0]:<25} {right[0]:<25}  │")
        print(f"│  {left_status:<25} {right_status:<25}  │")
        print(f"│                                                             │")
    
    print("""├─────────────────────────────────────────────────────────────┤
│  🚀 Ouvrir Tous les Dashboards   📄 Rapport Complet       │
└─────────────────────────────────────────────────────────────┘""")

def show_usage_instructions(working_dashboards):
    """Affiche les instructions d'utilisation"""
    
    print("\n4. 🎯 Instructions d'Utilisation")
    print("=" * 50)
    
    print("\n📋 Pour utiliser le popup d'orchestration:")
    print("1. Ouvrez: http://localhost:8010/bi_dashboard_dynamic.html")
    print("2. Cliquez sur 'Orchestrer' sur un projet")
    print("3. Le popup s'ouvrira automatiquement")
    print("4. Cliquez sur chaque connecteur pour ouvrir son dashboard")
    
    print(f"\n🔗 Dashboards fonctionnels ({len(working_dashboards)}/4):")
    for name, url in working_dashboards:
        print(f"   ✅ {name}: {url}")
    
    print(f"\n💡 Fonctionnalités du popup:")
    print("   - Affichage en temps réel du statut des connecteurs")
    print("   - Clic sur chaque connecteur pour ouvrir son dashboard")
    print("   - Bouton 'Ouvrir Tous les Dashboards' pour tout ouvrir")
    print("   - Bouton 'Rapport Complet' pour l'analyse détaillée")
    print("   - Génération automatique de charts et KPIs")

def main():
    print("🎭 BIMEX - Test du Popup d'Orchestration")
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Test 1: Orchestration
    connectors_results = test_orchestration_popup()
    
    # Test 2: Dashboards
    working_dashboards = test_individual_dashboards(connectors_results)
    
    # Test 3: Simulation du popup
    show_popup_simulation(connectors_results, working_dashboards)
    
    # Test 4: Instructions
    show_usage_instructions(working_dashboards)
    
    # Résumé final
    successful_connectors = sum(1 for result in connectors_results.values() if result.get('success'))
    total_connectors = len(connectors_results)
    
    print(f"\n🎉 RÉSUMÉ:")
    print(f"   📊 Connecteurs réussis: {successful_connectors}/{total_connectors}")
    print(f"   🔗 Dashboards fonctionnels: {len(working_dashboards)}/4")
    
    if successful_connectors >= 3 and len(working_dashboards) >= 3:
        print(f"   ✅ SYSTÈME OPÉRATIONNEL!")
        print(f"   🚀 Le popup d'orchestration est prêt à l'emploi")
    elif successful_connectors >= 2:
        print(f"   ⚠️ Système partiellement fonctionnel")
        print(f"   💡 Quelques connecteurs ont des problèmes mineurs")
    else:
        print(f"   ❌ Problèmes détectés")
        print(f"   💡 Vérifiez la configuration des connecteurs")

if __name__ == "__main__":
    main()
