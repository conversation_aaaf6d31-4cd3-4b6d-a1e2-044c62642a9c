#!/usr/bin/env python3
"""
🧪 Script de test pour vérifier le fonctionnement du dashboard BI
"""

import requests
import json
from datetime import datetime

def test_api_endpoints():
    """Teste les endpoints principaux de l'API"""
    
    base_url = "http://localhost:8010"
    
    print("🧪 Test des endpoints API BIMEX...")
    print("=" * 50)
    
    # Test 1: Statut BI
    try:
        response = requests.get(f"{base_url}/bi/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ /bi/status - OK")
            print(f"   Modules actifs: {sum(data['modules'].values())}/5")
            print(f"   Connecteurs: {len(data.get('connectors', []))}")
        else:
            print(f"❌ /bi/status - Erreur {response.status_code}")
    except Exception as e:
        print(f"❌ /bi/status - Exception: {e}")
    
    # Test 2: Liste des projets
    try:
        response = requests.get(f"{base_url}/projects", timeout=5)
        if response.status_code == 200:
            projects = response.json()
            print(f"✅ /projects - OK ({len(projects)} projets)")
            for project in projects[:3]:  # Afficher les 3 premiers
                print(f"   - {project.get('name', project.get('id', 'Unknown'))}")
        else:
            print(f"❌ /projects - Erreur {response.status_code}")
    except Exception as e:
        print(f"❌ /projects - Exception: {e}")
    
    # Test 3: Santé des services
    try:
        response = requests.get(f"{base_url}/bi/service-health", timeout=5)
        if response.status_code == 200:
            health = response.json()
            print(f"✅ /bi/service-health - OK")
            print(f"   Santé globale: {health.get('overall_health', 'Unknown')}")
        else:
            print(f"❌ /bi/service-health - Erreur {response.status_code}")
    except Exception as e:
        print(f"❌ /bi/service-health - Exception: {e}")
    
    # Test 4: Dashboard accessible
    try:
        response = requests.get(f"{base_url}/frontend/bi_dashboard_dynamic.html", timeout=5)
        if response.status_code == 200:
            print("✅ Dashboard BI - Accessible")
        else:
            print(f"❌ Dashboard BI - Erreur {response.status_code}")
    except Exception as e:
        print(f"❌ Dashboard BI - Exception: {e}")
    
    print("=" * 50)
    print("🎯 URLs d'accès:")
    print(f"   Dashboard BI: {base_url}/bi_dashboard_dynamic.html")
    print(f"   API Status:   {base_url}/bi/status")
    print(f"   Projets:      {base_url}/projects")
    print("=" * 50)

def test_orchestration_simulation():
    """Teste une orchestration simulée"""
    
    base_url = "http://localhost:8010"
    
    print("\n🎭 Test d'orchestration simulée...")
    print("=" * 50)
    
    # Utiliser le premier projet disponible
    try:
        response = requests.get(f"{base_url}/projects", timeout=5)
        if response.status_code == 200:
            projects = response.json()
            if projects:
                project_id = projects[0].get('id', projects[0].get('name', 'Duplex'))
                print(f"📋 Projet de test: {project_id}")
                
                # Test d'analyse de projet
                try:
                    response = requests.get(f"{base_url}/analyze-comprehensive-project/{project_id}", timeout=10)
                    if response.status_code == 200:
                        print("✅ Analyse de projet - OK")
                    else:
                        print(f"❌ Analyse de projet - Erreur {response.status_code}")
                except Exception as e:
                    print(f"❌ Analyse de projet - Exception: {e}")
                
            else:
                print("❌ Aucun projet disponible pour le test")
        else:
            print("❌ Impossible de récupérer la liste des projets")
    except Exception as e:
        print(f"❌ Erreur lors du test d'orchestration: {e}")

if __name__ == "__main__":
    print("🚀 BIMEX - Test du Dashboard BI Intelligent")
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_api_endpoints()
    test_orchestration_simulation()
    
    print("\n🎉 Tests terminés!")
    print("\n💡 Pour utiliser le dashboard:")
    print("   1. Assurez-vous que le serveur BIMEX tourne sur le port 8010")
    print("   2. Ouvrez http://localhost:8010/bi_dashboard_dynamic.html")
    print("   3. Cliquez sur 'Orchestrer' pour un projet")
    print("   4. Ou utilisez le bouton 'Orchestrateur Intelligent' (🎭)")
