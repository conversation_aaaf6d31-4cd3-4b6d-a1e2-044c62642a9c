#!/usr/bin/env python3
"""
🔍 Debug simple des connecteurs BIMEX
"""

import json
import requests
from pathlib import Path

def debug_config_file():
    """Debug direct du fichier de configuration"""
    
    print("📋 Debug du fichier de configuration")
    print("=" * 50)
    
    config_paths = [
        Path("backend/bi_config.json"),
        Path("bi_config.json"),
        Path("../backend/bi_config.json"),
        Path("./backend/bi_config.json")
    ]
    
    for path in config_paths:
        print(f"\n📁 Test du chemin: {path}")
        print(f"   Absolu: {path.absolute()}")
        print(f"   Existe: {path.exists()}")
        
        if path.exists():
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                print(f"   ✅ Fichier lu avec succès")
                print(f"   📊 Connecteurs: {len(config.get('connectors', []))}")
                
                for conn in config.get('connectors', []):
                    name = conn.get('name', 'Unknown')
                    active = conn.get('active', False)
                    endpoint = conn.get('endpoint', 'N/A')
                    print(f"     - {name}: {'✅ Actif' if active else '❌ Inactif'} ({endpoint})")
                
                return config
                
            except Exception as e:
                print(f"   ❌ Erreur lecture: {e}")
    
    return None

def test_api_endpoints():
    """Test des endpoints API avec requests"""
    
    print("\n🌐 Test des endpoints API")
    print("=" * 50)
    
    base_url = "http://localhost:8010"
    
    endpoints = [
        "/bi/status",
        "/bi/connectors-health"
    ]
    
    for endpoint in endpoints:
        print(f"\n🔗 Test de {endpoint}")
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Status: {response.status_code}")
                
                if endpoint == "/bi/status":
                    modules = data.get('modules', {})
                    real_connectors = modules.get('real_connectors', False)
                    print(f"   🔗 Module real_connectors: {'✅' if real_connectors else '❌'}")
                    
                    connectors = data.get('connectors', [])
                    print(f"   📊 Connecteurs: {len(connectors)}")
                    
                    for conn in connectors:
                        name = conn.get('name', 'Unknown')
                        active = conn.get('active', False)
                        online = conn.get('online', False)
                        print(f"     - {name}: {'✅' if active else '❌'} actif, {'🟢' if online else '🔴'} en ligne")
                    
                elif endpoint == "/bi/connectors-health":
                    summary = data.get('summary', {})
                    print(f"   📊 Total: {summary.get('total_connectors', 0)}")
                    print(f"   📊 Actifs: {summary.get('active_connectors', 0)}")
                    
                    detailed = data.get('detailed_status', {})
                    for name, status in detailed.items():
                        active = status.get('active', False)
                        online = status.get('online', False)
                        error = status.get('error', '')
                        print(f"     - {name}: {'✅' if active else '❌'} actif, {'🟢' if online else '🔴'} en ligne")
                        if error:
                            print(f"       Erreur: {error}")
                
            else:
                print(f"   ❌ Status: {response.status_code}")
                print(f"   Réponse: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ Erreur: {e}")

def test_individual_connectors():
    """Test des connecteurs individuels"""
    
    print("\n🧪 Test des connecteurs individuels")
    print("=" * 50)
    
    base_url = "http://localhost:8010"
    test_project = "basic2"
    
    connectors = ["Apache_Superset", "n8n_Workflows", "ERPNext", "IFCjs_Viewer"]
    
    for connector in connectors:
        print(f"\n🔗 Test de {connector}")
        try:
            response = requests.post(
                f"{base_url}/bi/test-connector/{connector}",
                data={"project_id": test_project},
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('success'):
                    print(f"   ✅ Test réussi")
                    
                    action_result = data.get('action_result', {})
                    if action_result.get('message'):
                        print(f"   📝 {action_result['message']}")
                    
                else:
                    print(f"   ❌ Test échoué: {data.get('error', 'Erreur inconnue')}")
                    
                    # Afficher les détails du statut du connecteur
                    connector_status = data.get('connector_status', {})
                    print(f"   📊 Détails du connecteur:")
                    print(f"     - Actif: {connector_status.get('active', False)}")
                    print(f"     - En ligne: {connector_status.get('online', False)}")
                    print(f"     - Endpoint: {connector_status.get('endpoint', 'N/A')}")
                    if connector_status.get('error'):
                        print(f"     - Erreur: {connector_status.get('error')}")
                
            else:
                print(f"   ❌ Erreur HTTP: {response.status_code}")
                print(f"   Réponse: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ Erreur: {e}")

def check_server_logs():
    """Vérifier les logs du serveur"""
    
    print("\n📋 Vérification des logs du serveur")
    print("=" * 50)
    
    print("💡 Pour voir les logs détaillés du serveur:")
    print("1. Regardez la console où le serveur BIMEX tourne")
    print("2. Cherchez les messages de debug du gestionnaire de connecteurs")
    print("3. Vérifiez s'il y a des erreurs de chargement de configuration")
    print("\n🔍 Messages à chercher:")
    print("   - '✅ Configuration chargée: X connecteurs'")
    print("   - '🔗 [nom]: ✅ Actif - [endpoint]'")
    print("   - '🔍 Vérification santé connecteur: [nom]'")

if __name__ == "__main__":
    print("🔍 BIMEX - Debug Simple des Connecteurs")
    print(f"⏰ {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Test 1: Fichier de configuration
    config = debug_config_file()
    
    # Test 2: Endpoints API
    test_api_endpoints()
    
    # Test 3: Connecteurs individuels
    test_individual_connectors()
    
    # Test 4: Instructions pour les logs
    check_server_logs()
    
    print("\n" + "=" * 60)
    print("🎉 Debug terminé!")
    print("\n💡 Solutions possibles:")
    print("1. Si 'real_connectors' est ❌ : Le module n'est pas chargé")
    print("2. Si les connecteurs sont ❌ inactifs : Problème de lecture de config")
    print("3. Si les connecteurs sont 🔴 hors ligne : Services non démarrés")
    print("4. Redémarrez le serveur BIMEX après avoir vérifié la config")
    print("\n🔧 Actions recommandées:")
    print("   - Vérifiez que backend/bi_config.json existe et est valide")
    print("   - Redémarrez le serveur BIMEX")
    print("   - Testez à nouveau les connecteurs")
