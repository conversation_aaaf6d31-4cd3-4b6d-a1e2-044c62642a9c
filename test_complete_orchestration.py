#!/usr/bin/env python3
"""
🎭 Test complet de l'orchestration BIMEX avec popup et dashboards
"""

import requests
import time
from datetime import datetime

def test_orchestration_complete():
    """Teste l'orchestration complète avec génération de dashboards"""
    
    print("🎭 Test de l'Orchestration Complète BIMEX")
    print("=" * 60)
    
    base_url = "http://localhost:8010"
    test_project = "basic2"
    
    print(f"🎯 Projet de test: {test_project}")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S')}")
    
    # Étape 1: Vérifier que le serveur est accessible
    print("\n1. 🔍 Vérification du serveur...")
    try:
        response = requests.get(f"{base_url}/bi/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            real_connectors = data.get('modules', {}).get('real_connectors', False)
            print(f"   ✅ Serveur accessible")
            print(f"   🔗 Module real_connectors: {'✅' if real_connectors else '❌'}")
            
            if not real_connectors:
                print("   ⚠️ ATTENTION: Module real_connectors non chargé")
                print("   💡 Redémarrez le serveur: cd backend && python main.py")
                return False
        else:
            print(f"   ❌ Erreur serveur: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Serveur non accessible: {e}")
        return False
    
    # Étape 2: Vérifier les connecteurs
    print("\n2. 🔗 Vérification des connecteurs...")
    try:
        response = requests.get(f"{base_url}/bi/connectors-health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            summary = data.get('summary', {})
            total = summary.get('total_connectors', 0)
            active = summary.get('active_connectors', 0)
            
            print(f"   📊 Connecteurs: {active}/{total} actifs")
            
            detailed = data.get('detailed_status', {})
            for name, status in detailed.items():
                active_status = status.get('active', False)
                online_status = status.get('online', False)
                icon = "✅" if active_status and online_status else "⚠️" if active_status else "❌"
                print(f"   {icon} {name}: {'Actif' if active_status else 'Inactif'}, {'En ligne' if online_status else 'Hors ligne'}")
            
            if active == 0:
                print("   ❌ Aucun connecteur actif!")
                return False
        else:
            print(f"   ❌ Erreur API connecteurs: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Erreur vérification connecteurs: {e}")
        return False
    
    # Étape 3: Lancer l'orchestration
    print(f"\n3. 🎭 Lancement de l'orchestration pour {test_project}...")
    try:
        response = requests.post(
            f"{base_url}/bi/launch-real-orchestration",
            data={"project_id": test_project},
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            connectors_executed = data.get('connectors_executed', 0)
            successful_connectors = data.get('successful_connectors', 0)
            
            print(f"   📊 Résultat: {'✅ Succès' if success else '⚠️ Partiel'}")
            print(f"   📊 Connecteurs exécutés: {connectors_executed}")
            print(f"   📊 Connecteurs réussis: {successful_connectors}")
            
            if data.get('errors'):
                print(f"   ❌ Erreurs: {', '.join(data['errors'])}")
            
            # Afficher les résultats détaillés
            results = data.get('results', {})
            connectors_results = results.get('connectors_results', {})
            
            print(f"\n   🔍 Détails par connecteur:")
            for name, result in connectors_results.items():
                status_icon = "✅" if result.get('success') else "❌"
                message = result.get('message', result.get('error', 'Pas de message'))
                print(f"     {status_icon} {name}: {message}")
                
                # Afficher les URLs générées
                if result.get('dashboard_url'):
                    print(f"       🔗 Dashboard: {result['dashboard_url']}")
                elif result.get('viewer_url'):
                    print(f"       🔗 Viewer: {result['viewer_url']}")
            
            return success or successful_connectors > 0
        else:
            print(f"   ❌ Erreur orchestration: {response.status_code}")
            print(f"   Réponse: {response.text[:200]}...")
            return False
    except Exception as e:
        print(f"   ❌ Erreur orchestration: {e}")
        return False

def test_individual_dashboards():
    """Teste l'accès aux dashboards individuels"""
    
    print("\n4. 📊 Test des dashboards individuels...")
    
    base_url = "http://localhost:8010"
    test_project = "basic2"
    
    dashboards = [
        ("Superset", f"{base_url}/superset-dashboard?project={test_project}"),
        ("IFC Viewer", f"{base_url}/ifc-viewer?project_id={test_project}"),
        ("ERP Dashboard", f"{base_url}/erp-dashboard?project_id={test_project}"),
        ("n8n", "http://localhost:5678")
    ]
    
    for name, url in dashboards:
        print(f"\n   🔗 Test {name}...")
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"     ✅ {name} accessible")
                print(f"     📄 Taille réponse: {len(response.content)} bytes")
            else:
                print(f"     ❌ {name} erreur: {response.status_code}")
        except Exception as e:
            print(f"     ❌ {name} erreur: {e}")

def show_usage_instructions():
    """Affiche les instructions d'utilisation"""
    
    print("\n" + "=" * 60)
    print("🎯 INSTRUCTIONS D'UTILISATION")
    print("=" * 60)
    
    print("\n1. 🌐 Ouvrir le dashboard principal:")
    print("   http://localhost:8010/bi_dashboard_dynamic.html")
    
    print("\n2. 🎭 Utiliser l'orchestration:")
    print("   - Cliquez sur 'Orchestrer' sur un projet")
    print("   - Un popup s'ouvrira avec les 4 connecteurs")
    print("   - Cliquez sur chaque connecteur pour voir son dashboard")
    
    print("\n3. 📊 Dashboards disponibles:")
    print("   - 📊 Superset: Graphiques BI générés automatiquement")
    print("   - 🏗️ IFC Viewer: Visualiseur 3D du modèle BIM")
    print("   - 🏢 ERP Dashboard: Gestion de projet intégrée")
    print("   - ⚙️ n8n: Workflows d'automatisation")
    
    print("\n4. 🔧 Test des connecteurs:")
    print("   - Utilisez les boutons dans le panneau de droite")
    print("   - Testez chaque connecteur individuellement")
    print("   - Vérifiez la santé des connecteurs")
    
    print("\n5. 🚀 Fonctionnalités avancées:")
    print("   - 'Ouvrir Tous les Dashboards': Ouvre tous les connecteurs")
    print("   - 'Rapport Complet': Génère une analyse complète")
    print("   - Export PDF des dashboards")
    
    print("\n💡 Note: Les dashboards sont générés automatiquement")
    print("   à partir de l'analyse des fichiers IFC du projet")

def main():
    print("🎭 BIMEX - Test Complet de l'Orchestration")
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test principal
    success = test_orchestration_complete()
    
    if success:
        print("\n✅ ORCHESTRATION RÉUSSIE!")
        
        # Test des dashboards individuels
        test_individual_dashboards()
        
        print("\n🎉 TOUS LES TESTS RÉUSSIS!")
        print("🚀 Le système d'orchestration est opérationnel")
        
    else:
        print("\n❌ ORCHESTRATION ÉCHOUÉE")
        print("💡 Vérifiez que le serveur BIMEX est redémarré")
        print("💡 Vérifiez que le module real_connectors est chargé")
    
    # Afficher les instructions
    show_usage_instructions()

if __name__ == "__main__":
    main()
