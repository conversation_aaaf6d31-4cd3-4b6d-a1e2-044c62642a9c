#!/usr/bin/env python3
"""
🔄 Script pour redémarrer et tester le serveur BIMEX avec la nouvelle configuration
"""

import time
import requests
import subprocess
import sys
import os
from datetime import datetime

def check_server_running():
    """Vérifie si le serveur est en cours d'exécution"""
    try:
        response = requests.get("http://localhost:8010/bi/status", timeout=3)
        return response.status_code == 200
    except:
        return False

def wait_for_server_ready(max_wait=30):
    """Attend que le serveur soit prêt"""
    print("⏳ Attente du serveur...")
    
    for i in range(max_wait):
        if check_server_running():
            print(f"✅ Serveur prêt après {i+1} secondes")
            return True
        time.sleep(1)
        if i % 5 == 0:
            print(f"   Tentative {i+1}/{max_wait}...")
    
    print("❌ Timeout - serveur non accessible")
    return False

def test_configuration():
    """Teste la nouvelle configuration"""
    print("\n🔧 Test de la nouvelle configuration")
    print("=" * 50)
    
    base_url = "http://localhost:8010"
    
    # Test 1: Statut BI
    print("\n1. 📊 Test du statut BI...")
    try:
        response = requests.get(f"{base_url}/bi/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            
            modules = data.get('modules', {})
            real_connectors = modules.get('real_connectors', False)
            
            print(f"   ✅ API accessible")
            print(f"   🔗 Module real_connectors: {'✅' if real_connectors else '❌'}")
            
            if not real_connectors:
                print("   ⚠️ Module real_connectors non chargé")
                return False
                
            connectors = data.get('connectors', [])
            print(f"   📊 Connecteurs détectés: {len(connectors)}")
            
            for conn in connectors:
                name = conn.get('name', 'Unknown')
                active = conn.get('active', False)
                online = conn.get('online', False)
                print(f"     {'✅' if active and online else '⚠️' if active else '❌'} {name}")
            
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False
    
    # Test 2: Santé des connecteurs
    print("\n2. 🏥 Test de santé des connecteurs...")
    try:
        response = requests.get(f"{base_url}/bi/connectors-health", timeout=15)
        if response.status_code == 200:
            data = response.json()
            
            summary = data.get('summary', {})
            total = summary.get('total_connectors', 0)
            active = summary.get('active_connectors', 0)
            
            print(f"   📊 Connecteurs: {active}/{total} actifs")
            
            detailed = data.get('detailed_status', {})
            expected_connectors = ['Apache_Superset', 'IFCjs_Viewer', 'n8n_Workflows', 'ERPNext']
            
            all_found = True
            for expected in expected_connectors:
                if expected in detailed:
                    status = detailed[expected]
                    active_status = status.get('active', False)
                    online_status = status.get('online', False)
                    print(f"   ✅ {expected}: {'Actif' if active_status else 'Inactif'}, {'En ligne' if online_status else 'Hors ligne'}")
                else:
                    print(f"   ❌ {expected}: Non trouvé")
                    all_found = False
            
            return all_found
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_orchestration():
    """Teste l'orchestration avec la nouvelle configuration"""
    print("\n3. 🎭 Test d'orchestration...")
    
    base_url = "http://localhost:8010"
    test_project = "basic2"
    
    try:
        response = requests.post(
            f"{base_url}/bi/launch-real-orchestration",
            data={"project_id": test_project},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            success = data.get('success', False)
            connectors_executed = data.get('connectors_executed', 0)
            successful_connectors = data.get('successful_connectors', 0)
            
            print(f"   📊 Résultat: {'✅ Succès' if success else '⚠️ Partiel'}")
            print(f"   📊 Connecteurs exécutés: {connectors_executed}")
            print(f"   📊 Connecteurs réussis: {successful_connectors}")
            
            if data.get('errors'):
                print(f"   ❌ Erreurs: {', '.join(data['errors'])}")
            
            return successful_connectors > 0
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_dashboards():
    """Teste l'accès aux dashboards"""
    print("\n4. 📊 Test des dashboards...")
    
    base_url = "http://localhost:8010"
    
    dashboards = [
        ("Superset", f"{base_url}/superset-dashboard"),
        ("IFC Viewer", f"{base_url}/ifc-viewer"),
        ("ERP Dashboard", f"{base_url}/erp-dashboard")
    ]
    
    all_working = True
    
    for name, url in dashboards:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"   ✅ {name}: Accessible")
            else:
                print(f"   ❌ {name}: Erreur {response.status_code}")
                all_working = False
        except Exception as e:
            print(f"   ❌ {name}: {e}")
            all_working = False
    
    return all_working

def show_instructions():
    """Affiche les instructions finales"""
    print("\n" + "=" * 60)
    print("🎯 INSTRUCTIONS D'UTILISATION")
    print("=" * 60)
    
    print("\n1. 🌐 Ouvrir le dashboard:")
    print("   http://localhost:8010/bi_dashboard_dynamic.html")
    
    print("\n2. 🎭 Utiliser l'orchestration:")
    print("   - Cliquez sur 'Orchestrer' sur un projet")
    print("   - Le popup s'ouvrira avec les 4 connecteurs")
    print("   - Cliquez sur chaque connecteur pour voir son dashboard")
    
    print("\n3. 📊 Dashboards disponibles:")
    print("   - Apache Superset: http://localhost:8010/superset-dashboard")
    print("   - IFC Viewer: http://localhost:8010/ifc-viewer")
    print("   - ERP Dashboard: http://localhost:8010/erp-dashboard")
    print("   - n8n Workflows: http://localhost:5678")
    
    print("\n4. 🔧 Fonctionnalités:")
    print("   - Génération automatique de dashboards")
    print("   - Analyse IFC en temps réel")
    print("   - Popup d'orchestration interactif")
    print("   - Charts et KPIs automatiques")

def main():
    print("🔄 BIMEX - Test de la Nouvelle Configuration")
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Vérifier si le serveur est en cours d'exécution
    if check_server_running():
        print("✅ Serveur BIMEX détecté")
        
        # Tester la configuration
        config_ok = test_configuration()
        
        if config_ok:
            print("\n✅ Configuration correcte")
            
            # Tester l'orchestration
            orchestration_ok = test_orchestration()
            
            if orchestration_ok:
                print("\n✅ Orchestration fonctionnelle")
                
                # Tester les dashboards
                dashboards_ok = test_dashboards()
                
                if dashboards_ok:
                    print("\n🎉 TOUS LES TESTS RÉUSSIS!")
                    print("🚀 Le système est opérationnel")
                else:
                    print("\n⚠️ Certains dashboards ont des problèmes")
            else:
                print("\n⚠️ Problème avec l'orchestration")
        else:
            print("\n❌ Problème de configuration")
            print("💡 Vérifiez que le serveur a été redémarré")
    else:
        print("❌ Serveur BIMEX non accessible")
        print("💡 Démarrez le serveur avec:")
        print("   cd backend")
        print("   python -m uvicorn main:app --host 0.0.0.0 --port 8010 --reload")
    
    # Afficher les instructions
    show_instructions()

if __name__ == "__main__":
    main()
